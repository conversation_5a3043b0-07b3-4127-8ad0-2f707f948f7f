<?php
/**
 * Validation Class
 * Quản Lý Cà Phê - Coffee Shop Management System
 */

class Validator {
    private $errors = [];
    private $data = [];
    
    /**
     * Validate data against rules
     */
    public function validate($data, $rules) {
        $this->data = $data;
        $this->errors = [];
        
        foreach ($rules as $field => $ruleSet) {
            $this->validateField($field, $ruleSet);
        }
        
        return empty($this->errors);
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get errors for specific field
     */
    public function getFieldErrors($field) {
        return $this->errors[$field] ?? [];
    }
    
    /**
     * Check if field has errors
     */
    public function hasErrors($field = null) {
        if ($field) {
            return isset($this->errors[$field]);
        }
        return !empty($this->errors);
    }
    
    /**
     * Validate individual field
     */
    private function validateField($field, $rules) {
        $value = $this->data[$field] ?? null;
        $rules = is_string($rules) ? explode('|', $rules) : $rules;
        
        foreach ($rules as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }
    
    /**
     * Apply validation rule
     */
    private function applyRule($field, $value, $rule) {
        $params = [];
        
        // Parse rule parameters
        if (strpos($rule, ':') !== false) {
            list($rule, $paramString) = explode(':', $rule, 2);
            $params = explode(',', $paramString);
        }
        
        $methodName = 'validate' . ucfirst($rule);
        
        if (method_exists($this, $methodName)) {
            $this->$methodName($field, $value, $params);
        }
    }
    
    /**
     * Add error message
     */
    private function addError($field, $message) {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        $this->errors[$field][] = $message;
    }
    
    /**
     * Required validation
     */
    private function validateRequired($field, $value, $params) {
        if (empty($value) && $value !== '0') {
            $this->addError($field, "Trường {$field} là bắt buộc.");
        }
    }
    
    /**
     * Email validation
     */
    private function validateEmail($field, $value, $params) {
        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $this->addError($field, "Trường {$field} phải là email hợp lệ.");
        }
    }
    
    /**
     * Minimum length validation
     */
    private function validateMin($field, $value, $params) {
        $min = (int)$params[0];
        if (!empty($value) && strlen($value) < $min) {
            $this->addError($field, "Trường {$field} phải có ít nhất {$min} ký tự.");
        }
    }
    
    /**
     * Maximum length validation
     */
    private function validateMax($field, $value, $params) {
        $max = (int)$params[0];
        if (!empty($value) && strlen($value) > $max) {
            $this->addError($field, "Trường {$field} không được vượt quá {$max} ký tự.");
        }
    }
    
    /**
     * Numeric validation
     */
    private function validateNumeric($field, $value, $params) {
        if (!empty($value) && !is_numeric($value)) {
            $this->addError($field, "Trường {$field} phải là số.");
        }
    }
    
    /**
     * Integer validation
     */
    private function validateInteger($field, $value, $params) {
        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
            $this->addError($field, "Trường {$field} phải là số nguyên.");
        }
    }
    
    /**
     * Decimal validation
     */
    private function validateDecimal($field, $value, $params) {
        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_FLOAT)) {
            $this->addError($field, "Trường {$field} phải là số thập phân.");
        }
    }
    
    /**
     * Phone validation
     */
    private function validatePhone($field, $value, $params) {
        if (!empty($value) && !preg_match('/^[0-9+\-\s\(\)]+$/', $value)) {
            $this->addError($field, "Trường {$field} không phải là số điện thoại hợp lệ.");
        }
    }
    
    /**
     * Date validation
     */
    private function validateDate($field, $value, $params) {
        if (!empty($value)) {
            $date = DateTime::createFromFormat('Y-m-d', $value);
            if (!$date || $date->format('Y-m-d') !== $value) {
                $this->addError($field, "Trường {$field} phải là ngày hợp lệ (YYYY-MM-DD).");
            }
        }
    }
    
    /**
     * Datetime validation
     */
    private function validateDatetime($field, $value, $params) {
        if (!empty($value)) {
            $datetime = DateTime::createFromFormat('Y-m-d H:i:s', $value);
            if (!$datetime || $datetime->format('Y-m-d H:i:s') !== $value) {
                $this->addError($field, "Trường {$field} phải là ngày giờ hợp lệ (YYYY-MM-DD HH:MM:SS).");
            }
        }
    }
    
    /**
     * In array validation
     */
    private function validateIn($field, $value, $params) {
        if (!empty($value) && !in_array($value, $params)) {
            $this->addError($field, "Trường {$field} phải là một trong các giá trị: " . implode(', ', $params));
        }
    }
    
    /**
     * Unique validation (database)
     */
    private function validateUnique($field, $value, $params) {
        if (!empty($value)) {
            $table = $params[0];
            $column = $params[1] ?? $field;
            $except = $params[2] ?? null;
            
            $db = Database::getInstance();
            $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = ?";
            $sqlParams = [$value];
            
            if ($except) {
                $sql .= " AND id != ?";
                $sqlParams[] = $except;
            }
            
            $result = $db->fetch($sql, $sqlParams);
            if ($result && $result['count'] > 0) {
                $this->addError($field, "Trường {$field} đã tồn tại.");
            }
        }
    }
    
    /**
     * Exists validation (database)
     */
    private function validateExists($field, $value, $params) {
        if (!empty($value)) {
            $table = $params[0];
            $column = $params[1] ?? $field;
            
            $db = Database::getInstance();
            $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = ?";
            $result = $db->fetch($sql, [$value]);
            
            if (!$result || $result['count'] == 0) {
                $this->addError($field, "Trường {$field} không tồn tại.");
            }
        }
    }
    
    /**
     * Confirmed validation (password confirmation)
     */
    private function validateConfirmed($field, $value, $params) {
        $confirmField = $field . '_confirmation';
        $confirmValue = $this->data[$confirmField] ?? null;
        
        if ($value !== $confirmValue) {
            $this->addError($field, "Trường {$field} không khớp với xác nhận.");
        }
    }
    
    /**
     * File validation
     */
    private function validateFile($field, $value, $params) {
        if (isset($_FILES[$field]) && $_FILES[$field]['error'] !== UPLOAD_ERR_NO_FILE) {
            $file = $_FILES[$field];
            
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $this->addError($field, "Lỗi tải file {$field}.");
                return;
            }
            
            // Check file size
            if ($file['size'] > MAX_FILE_SIZE) {
                $this->addError($field, "File {$field} quá lớn.");
                return;
            }
            
            // Check file type if specified
            if (!empty($params)) {
                $allowedTypes = $params;
                $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                
                if (!in_array($fileExtension, $allowedTypes)) {
                    $this->addError($field, "File {$field} phải có định dạng: " . implode(', ', $allowedTypes));
                }
            }
        }
    }
}
