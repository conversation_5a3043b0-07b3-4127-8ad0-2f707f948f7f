<?php
/**
 * View Class
 * Quản Lý <PERSON> Phê - Coffee Shop Management System
 */

class View {
    private $data = [];
    private $layout = 'layouts/app';
    
    /**
     * Render a view
     */
    public function render($view, $data = []) {
        $this->data = array_merge($this->data, $data);
        
        // Extract data to variables
        extract($this->data);
        
        // Start output buffering
        ob_start();
        
        // Include the view file
        $viewFile = VIEW_PATH . str_replace('.', '/', $view) . '.php';
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new Exception("View file not found: $viewFile");
        }
        
        // Get the content
        $content = ob_get_clean();
        
        // If layout is set, render with layout
        if ($this->layout) {
            $this->renderWithLayout($content);
        } else {
            echo $content;
        }
    }
    
    /**
     * Render view with layout
     */
    private function renderWithLayout($content) {
        // Extract data to variables
        extract($this->data);
        
        // Include the layout file
        $layoutFile = VIEW_PATH . str_replace('.', '/', $this->layout) . '.php';
        
        if (file_exists($layoutFile)) {
            include $layoutFile;
        } else {
            echo $content; // Fallback to content only
        }
    }
    
    /**
     * Set layout
     */
    public function setLayout($layout) {
        $this->layout = $layout;
    }
    
    /**
     * Disable layout
     */
    public function withoutLayout() {
        $this->layout = null;
    }
    
    /**
     * Set data
     */
    public function with($key, $value = null) {
        if (is_array($key)) {
            $this->data = array_merge($this->data, $key);
        } else {
            $this->data[$key] = $value;
        }
        return $this;
    }
    
    /**
     * Include a partial view
     */
    public function partial($view, $data = []) {
        $partialData = array_merge($this->data, $data);
        extract($partialData);
        
        $viewFile = VIEW_PATH . 'partials/' . str_replace('.', '/', $view) . '.php';
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            echo "<!-- Partial not found: $viewFile -->";
        }
    }
    
    /**
     * Include a component
     */
    public function component($component, $data = []) {
        $componentData = array_merge($this->data, $data);
        extract($componentData);
        
        $componentFile = VIEW_PATH . 'components/' . str_replace('.', '/', $component) . '.php';
        
        if (file_exists($componentFile)) {
            include $componentFile;
        } else {
            echo "<!-- Component not found: $componentFile -->";
        }
    }
    
    /**
     * Escape HTML
     */
    public function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Generate URL
     */
    public function url($path = '') {
        return BASE_URL . ltrim($path, '/');
    }
    
    /**
     * Generate asset URL
     */
    public function asset($path) {
        return BASE_URL . 'public/' . ltrim($path, '/');
    }
    
    /**
     * Check if user is authenticated
     */
    public function isAuth() {
        return Auth::check();
    }
    
    /**
     * Get current user
     */
    public function user() {
        return Auth::user();
    }
    
    /**
     * Check if user has permission
     */
    public function hasPermission($permission) {
        return Auth::hasPermission($permission);
    }
    
    /**
     * Get flash message
     */
    public function flash($type) {
        return Session::getFlash($type);
    }
    
    /**
     * Get old input value
     */
    public function old($key, $default = '') {
        return Session::getFlash('old_' . $key, $default);
    }
    
    /**
     * Get validation errors
     */
    public function errors($key = null) {
        if ($key) {
            return Session::getFlash('errors_' . $key, []);
        }
        return Session::getFlash('errors', []);
    }
    
    /**
     * Format currency
     */
    public function currency($amount) {
        return number_format($amount, 0, ',', '.') . ' ' . CURRENCY_SYMBOL;
    }
    
    /**
     * Format date
     */
    public function date($date, $format = DATE_FORMAT) {
        if (empty($date)) return '';
        return date($format, strtotime($date));
    }
    
    /**
     * Format datetime
     */
    public function datetime($datetime, $format = DATETIME_FORMAT) {
        if (empty($datetime)) return '';
        return date($format, strtotime($datetime));
    }
    
    /**
     * Generate CSRF token field
     */
    public function csrfField() {
        return '<input type="hidden" name="' . CSRF_TOKEN_NAME . '" value="' . Session::get('csrf_token', '') . '">';
    }
    
    /**
     * Get CSRF token
     */
    public function csrfToken() {
        return Session::get('csrf_token', '');
    }
    
    /**
     * Truncate text
     */
    public function truncate($text, $length = 100, $suffix = '...') {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        return mb_substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Generate pagination links
     */
    public function pagination($pagination, $url = '') {
        if ($pagination['last_page'] <= 1) {
            return '';
        }
        
        $html = '<nav aria-label="Pagination">';
        $html .= '<ul class="pagination justify-content-center">';
        
        // Previous page
        if ($pagination['current_page'] > 1) {
            $prevPage = $pagination['current_page'] - 1;
            $html .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . $prevPage . '">Trước</a></li>';
        }
        
        // Page numbers
        for ($i = 1; $i <= $pagination['last_page']; $i++) {
            $active = $i == $pagination['current_page'] ? 'active' : '';
            $html .= '<li class="page-item ' . $active . '"><a class="page-link" href="' . $url . '?page=' . $i . '">' . $i . '</a></li>';
        }
        
        // Next page
        if ($pagination['current_page'] < $pagination['last_page']) {
            $nextPage = $pagination['current_page'] + 1;
            $html .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . $nextPage . '">Sau</a></li>';
        }
        
        $html .= '</ul>';
        $html .= '</nav>';
        
        return $html;
    }
}
