-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               8.0.30 - MySQL Community Server - GPL
-- Server OS:                    Win64
-- HeidiSQL Version:             12.1.0.6537
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIG<PERSON>_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- Dumping database structure for quanlycafe
DROP DATABASE IF EXISTS `quanlycafe`;
CREATE DATABASE IF NOT EXISTS `quanlycafe` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `quanlycafe`;

-- Dumping structure for table quanlycafe.areas
DROP TABLE IF EXISTS `areas`;
CREATE TABLE IF NOT EXISTS `areas` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `floor` int DEFAULT '1',
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.areas: ~3 rows (approximately)
INSERT INTO `areas` (`id`, `name`, `description`, `floor`, `status`, `created_at`, `updated_at`) VALUES
	(1, 'Main Area', 'Main dining area on ground floor', 1, 'active', '2025-06-08 07:08:20', '2025-06-08 07:08:20'),
	(2, 'Outdoor', 'Outdoor seating with garden view', 1, 'active', '2025-06-08 07:08:20', '2025-06-08 07:08:20'),
	(3, 'VIP Room', 'Private VIP room for events', 1, 'active', '2025-06-08 07:08:20', '2025-06-08 07:08:20');

-- Dumping structure for table quanlycafe.attendance
DROP TABLE IF EXISTS `attendance`;
CREATE TABLE IF NOT EXISTS `attendance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_id` int NOT NULL,
  `check_in` datetime NOT NULL,
  `check_out` datetime DEFAULT NULL,
  `status` enum('present','late','absent','half-day') DEFAULT 'present',
  `note` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.attendance: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.customers
DROP TABLE IF EXISTS `customers`;
CREATE TABLE IF NOT EXISTS `customers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.customers: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.discounts
DROP TABLE IF EXISTS `discounts`;
CREATE TABLE IF NOT EXISTS `discounts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(20) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `type` enum('percentage','fixed') NOT NULL,
  `value` decimal(10,2) NOT NULL,
  `min_order_value` decimal(10,2) DEFAULT '0.00',
  `max_discount_amount` decimal(10,2) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `usage_limit` int DEFAULT NULL,
  `usage_count` int DEFAULT '0',
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.discounts: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.employees
DROP TABLE IF EXISTS `employees`;
CREATE TABLE IF NOT EXISTS `employees` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text,
  `id_number` varchar(20) DEFAULT NULL,
  `position` varchar(50) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `employees_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.employees: ~0 rows (approximately)
INSERT INTO `employees` (`id`, `user_id`, `first_name`, `last_name`, `gender`, `date_of_birth`, `phone`, `address`, `id_number`, `position`, `hire_date`, `salary`, `avatar`, `created_at`, `updated_at`) VALUES
	(1, NULL, 'Nguyễn', 'Văn A', 'male', NULL, '0901234567', NULL, NULL, 'Manager', '2023-01-01', 15000000.00, NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(2, NULL, 'Trần', 'Thị B', 'female', NULL, '0907654321', NULL, NULL, 'Cashier', '2023-02-01', 8000000.00, NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(3, NULL, 'Lê', 'Văn C', 'male', NULL, '0909876543', NULL, NULL, 'Server', '2023-03-01', 7000000.00, NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21');

-- Dumping structure for table quanlycafe.employee_shifts
DROP TABLE IF EXISTS `employee_shifts`;
CREATE TABLE IF NOT EXISTS `employee_shifts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_id` int NOT NULL,
  `shift_id` int NOT NULL,
  `day_of_week` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_id` (`employee_id`,`shift_id`,`day_of_week`),
  KEY `shift_id` (`shift_id`),
  CONSTRAINT `employee_shifts_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `employee_shifts_ibfk_2` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.employee_shifts: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.faqs
DROP TABLE IF EXISTS `faqs`;
CREATE TABLE IF NOT EXISTS `faqs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `question` text NOT NULL,
  `answer` text NOT NULL,
  `category` varchar(50) DEFAULT NULL,
  `display_order` int DEFAULT '0',
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.faqs: ~3 rows (approximately)
INSERT INTO `faqs` (`id`, `question`, `answer`, `category`, `display_order`, `status`, `created_at`, `updated_at`) VALUES
	(1, 'Làm thế nào để tạo đơn hàng mới?', 'Bạn có thể tạo đơn hàng mới bằng cách vào mục Quản lý đơn hàng, nhấn nút Tạo đơn hàng, sau đó chọn bàn và thêm các món ăn vào đơn.', 'Đơn hàng', 1, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(2, 'Làm thế nào để đặt bàn trước?', 'Vào mục Quản lý bàn, chọn chức năng Đặt bàn, điền thông tin khách hàng, ngày giờ đặt và lưu lại.', 'Đặt bàn', 2, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(3, 'Cách kiểm tra tồn kho?', 'Vào mục Quản lý kho, chọn Báo cáo tồn kho để xem danh sách các nguyên liệu và số lượng hiện có.', 'Kho', 3, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21');

-- Dumping structure for procedure quanlycafe.generate_revenue_report
DROP PROCEDURE IF EXISTS `generate_revenue_report`;
DELIMITER //
CREATE PROCEDURE `generate_revenue_report`(IN p_start_date DATE, IN p_end_date DATE)
BEGIN
    SELECT 
        DATE(i.issued_at) AS report_date,
        COUNT(i.id) AS invoice_count,
        SUM(i.subtotal) AS total_subtotal,
        SUM(i.tax_amount) AS total_tax,
        SUM(i.discount_amount) AS total_discount,
        SUM(i.total_amount) AS total_revenue,
        SUM(CASE WHEN p.payment_method = 'cash' THEN p.amount ELSE 0 END) AS cash_payments,
        SUM(CASE WHEN p.payment_method = 'card' THEN p.amount ELSE 0 END) AS card_payments,
        SUM(CASE WHEN p.payment_method IN ('momo', 'zalopay', 'vnpay') THEN p.amount ELSE 0 END) AS e_wallet_payments
    FROM invoices i
    JOIN payments p ON i.id = p.invoice_id
    WHERE DATE(i.issued_at) BETWEEN p_start_date AND p_end_date
    GROUP BY DATE(i.issued_at)
    ORDER BY DATE(i.issued_at);
END//
DELIMITER ;

-- Dumping structure for procedure quanlycafe.get_best_selling_items
DROP PROCEDURE IF EXISTS `get_best_selling_items`;
DELIMITER //
CREATE PROCEDURE `get_best_selling_items`(IN p_start_date DATE, IN p_end_date DATE, IN p_limit INT)
BEGIN
    SELECT 
        mi.id,
        mi.name,
        mc.name AS category,
        SUM(oi.quantity) AS total_quantity,
        SUM(oi.total_price) AS total_revenue
    FROM order_items oi
    JOIN menu_items mi ON oi.menu_item_id = mi.id
    JOIN menu_categories mc ON mi.category_id = mc.id
    JOIN orders o ON oi.order_id = o.id
    WHERE 
        DATE(o.ordered_at) BETWEEN p_start_date AND p_end_date
        AND o.status = 'completed'
    GROUP BY mi.id, mi.name, mc.name
    ORDER BY total_quantity DESC
    LIMIT p_limit;
END//
DELIMITER ;

-- Dumping structure for table quanlycafe.invoices
DROP TABLE IF EXISTS `invoices`;
CREATE TABLE IF NOT EXISTS `invoices` (
  `id` int NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(20) DEFAULT NULL,
  `order_id` int NOT NULL,
  `customer_id` int DEFAULT NULL,
  `employee_id` int DEFAULT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `tax_amount` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_amount` decimal(10,2) NOT NULL,
  `paid_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `change_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `payment_status` enum('pending','partial','paid','refunded') DEFAULT 'pending',
  `notes` text,
  `issued_at` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invoice_number` (`invoice_number`),
  KEY `order_id` (`order_id`),
  KEY `customer_id` (`customer_id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `invoices_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `invoices_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `invoices_ibfk_3` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.invoices: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.materials
DROP TABLE IF EXISTS `materials`;
CREATE TABLE IF NOT EXISTS `materials` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(20) DEFAULT NULL,
  `category_id` int DEFAULT NULL,
  `unit` varchar(20) NOT NULL,
  `cost_price` decimal(10,2) NOT NULL,
  `current_stock` decimal(10,2) DEFAULT '0.00',
  `min_stock_level` decimal(10,2) DEFAULT '0.00',
  `description` text,
  `image` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `materials_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `material_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.materials: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.material_categories
DROP TABLE IF EXISTS `material_categories`;
CREATE TABLE IF NOT EXISTS `material_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `parent_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `material_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `material_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.material_categories: ~6 rows (approximately)
INSERT INTO `material_categories` (`id`, `name`, `description`, `parent_id`, `created_at`, `updated_at`) VALUES
	(1, 'Coffee Beans', 'Various types of coffee beans', NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(2, 'Milk & Dairy', 'Milk and other dairy products', NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(3, 'Syrups & Flavorings', 'Flavor syrups and additives', NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(4, 'Teas', 'Various types of tea leaves and bags', NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(5, 'Fruits', 'Fresh fruits for beverages and desserts', NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(6, 'Bakery Items', 'Ingredients for bakery products', NULL, '2025-06-08 07:08:21', '2025-06-08 07:08:21');

-- Dumping structure for table quanlycafe.menu_categories
DROP TABLE IF EXISTS `menu_categories`;
CREATE TABLE IF NOT EXISTS `menu_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` int DEFAULT NULL,
  `display_order` int DEFAULT '0',
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `menu_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `menu_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.menu_categories: ~5 rows (approximately)
INSERT INTO `menu_categories` (`id`, `name`, `description`, `image`, `parent_id`, `display_order`, `status`, `created_at`, `updated_at`) VALUES
	(1, 'Coffee', 'Variety of coffee beverages', NULL, NULL, 1, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(2, 'Tea', 'Selection of tea drinks', NULL, NULL, 2, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(3, 'Smoothies', 'Refreshing fruit smoothies', NULL, NULL, 3, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(4, 'Snacks', 'Light snacks and pastries', NULL, NULL, 4, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(5, 'Desserts', 'Sweet treats and desserts', NULL, NULL, 5, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21');

-- Dumping structure for table quanlycafe.menu_items
DROP TABLE IF EXISTS `menu_items`;
CREATE TABLE IF NOT EXISTS `menu_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `category_id` int DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `discount_price` decimal(10,2) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_featured` tinyint(1) DEFAULT '0',
  `is_available` tinyint(1) DEFAULT '1',
  `preparation_time` int DEFAULT '10',
  `display_order` int DEFAULT '0',
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `menu_items_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `menu_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.menu_items: ~11 rows (approximately)
INSERT INTO `menu_items` (`id`, `name`, `description`, `category_id`, `price`, `discount_price`, `image`, `is_featured`, `is_available`, `preparation_time`, `display_order`, `status`, `created_at`, `updated_at`) VALUES
	(1, 'Cà Phê Đen', 'Traditional Vietnamese black coffee', 1, 25000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(2, 'Cà Phê Sữa', 'Vietnamese coffee with condensed milk', 1, 30000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(3, 'Bạc Xỉu', 'White Vietnamese coffee with more milk', 1, 35000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(4, 'Espresso', 'Strong coffee shot', 1, 30000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(5, 'Cappuccino', 'Espresso with steamed milk and foam', 1, 45000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(6, 'Trà Sen Vàng', 'Lotus tea', 2, 35000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(7, 'Trà Đào', 'Peach tea with fresh peach slices', 2, 40000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(8, 'Sinh Tố Xoài', 'Mango smoothie', 3, 45000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(9, 'Sinh Tố Dâu', 'Strawberry smoothie', 3, 45000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(10, 'Bánh Croissant', 'Butter croissant', 4, 25000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(11, 'Bánh Tiramisu', 'Classic tiramisu cake', 5, 35000.00, NULL, NULL, 0, 1, 10, 0, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21');

-- Dumping structure for table quanlycafe.menu_item_materials
DROP TABLE IF EXISTS `menu_item_materials`;
CREATE TABLE IF NOT EXISTS `menu_item_materials` (
  `id` int NOT NULL AUTO_INCREMENT,
  `menu_item_id` int NOT NULL,
  `material_id` int NOT NULL,
  `quantity` decimal(10,3) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `menu_item_id` (`menu_item_id`),
  KEY `material_id` (`material_id`),
  CONSTRAINT `menu_item_materials_ibfk_1` FOREIGN KEY (`menu_item_id`) REFERENCES `menu_items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `menu_item_materials_ibfk_2` FOREIGN KEY (`material_id`) REFERENCES `materials` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.menu_item_materials: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.orders
DROP TABLE IF EXISTS `orders`;
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_number` varchar(20) DEFAULT NULL,
  `table_id` int DEFAULT NULL,
  `customer_id` int DEFAULT NULL,
  `employee_id` int DEFAULT NULL,
  `order_type` enum('dine-in','takeout','delivery') DEFAULT 'dine-in',
  `status` enum('pending','confirmed','preparing','ready','completed','cancelled') DEFAULT 'pending',
  `subtotal` decimal(10,2) NOT NULL DEFAULT '0.00',
  `tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `notes` text,
  `ordered_at` datetime NOT NULL,
  `completed_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `table_id` (`table_id`),
  KEY `customer_id` (`customer_id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`table_id`) REFERENCES `tables` (`id`) ON DELETE SET NULL,
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.orders: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.order_items
DROP TABLE IF EXISTS `order_items`;
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `menu_item_id` int NOT NULL,
  `quantity` int NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `discount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_price` decimal(10,2) NOT NULL,
  `notes` text,
  `status` enum('pending','preparing','ready','served','cancelled') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `menu_item_id` (`menu_item_id`),
  CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`menu_item_id`) REFERENCES `menu_items` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.order_items: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.payments
DROP TABLE IF EXISTS `payments`;
CREATE TABLE IF NOT EXISTS `payments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `invoice_id` int NOT NULL,
  `payment_method` enum('cash','card','momo','zalopay','vnpay','other') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `transaction_reference` varchar(100) DEFAULT NULL,
  `payment_date` datetime NOT NULL,
  `status` enum('completed','pending','failed','refunded') DEFAULT 'completed',
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `invoice_id` (`invoice_id`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.payments: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.reservations
DROP TABLE IF EXISTS `reservations`;
CREATE TABLE IF NOT EXISTS `reservations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `table_id` int DEFAULT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `party_size` int NOT NULL,
  `reservation_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time DEFAULT NULL,
  `status` enum('confirmed','seated','completed','cancelled','no-show') DEFAULT 'confirmed',
  `notes` text,
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `table_id` (`table_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `reservations_ibfk_1` FOREIGN KEY (`table_id`) REFERENCES `tables` (`id`) ON DELETE SET NULL,
  CONSTRAINT `reservations_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.reservations: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.roles
DROP TABLE IF EXISTS `roles`;
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `permissions` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.roles: ~5 rows (approximately)
INSERT INTO `roles` (`id`, `name`, `description`, `permissions`, `created_at`, `updated_at`) VALUES
	(1, 'Admin', 'Full system access', '{"all": true}', '2025-06-08 07:08:20', '2025-06-08 07:08:20'),
	(2, 'Manager', 'Manage operations, staff, and reports', '{"menu": true, "areas": true, "orders": true, "tables": true, "reports": true, "invoices": true, "dashboard": true, "employees": true, "inventory": true}', '2025-06-08 07:08:20', '2025-06-08 07:08:20'),
	(3, 'Cashier', 'Handle orders and payments', '{"orders": true, "tables": {"view": true}, "invoices": true, "dashboard": true}', '2025-06-08 07:08:20', '2025-06-08 07:08:20'),
	(4, 'Server', 'Take orders and serve customers', '{"orders": {"view": true, "create": true, "update": true}, "tables": {"view": true}, "dashboard": true}', '2025-06-08 07:08:20', '2025-06-08 07:08:20'),
	(5, 'Inventory Manager', 'Manage inventory and suppliers', '{"dashboard": true, "inventory": true, "suppliers": true}', '2025-06-08 07:08:20', '2025-06-08 07:08:20');

-- Dumping structure for table quanlycafe.shifts
DROP TABLE IF EXISTS `shifts`;
CREATE TABLE IF NOT EXISTS `shifts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.shifts: ~3 rows (approximately)
INSERT INTO `shifts` (`id`, `name`, `start_time`, `end_time`, `created_at`, `updated_at`) VALUES
	(1, 'Morning', '07:00:00', '15:00:00', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(2, 'Afternoon', '14:00:00', '22:00:00', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(3, 'Evening', '17:00:00', '01:00:00', '2025-06-08 07:08:21', '2025-06-08 07:08:21');

-- Dumping structure for table quanlycafe.stock_in
DROP TABLE IF EXISTS `stock_in`;
CREATE TABLE IF NOT EXISTS `stock_in` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(20) NOT NULL,
  `supplier_id` int DEFAULT NULL,
  `employee_id` int DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `note` text,
  `transaction_date` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `supplier_id` (`supplier_id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `stock_in_ibfk_1` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `stock_in_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.stock_in: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.stock_in_items
DROP TABLE IF EXISTS `stock_in_items`;
CREATE TABLE IF NOT EXISTS `stock_in_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `stock_in_id` int NOT NULL,
  `material_id` int NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `stock_in_id` (`stock_in_id`),
  KEY `material_id` (`material_id`),
  CONSTRAINT `stock_in_items_ibfk_1` FOREIGN KEY (`stock_in_id`) REFERENCES `stock_in` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stock_in_items_ibfk_2` FOREIGN KEY (`material_id`) REFERENCES `materials` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.stock_in_items: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.stock_out
DROP TABLE IF EXISTS `stock_out`;
CREATE TABLE IF NOT EXISTS `stock_out` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(20) NOT NULL,
  `employee_id` int DEFAULT NULL,
  `reason` enum('production','damage','expired','transfer','other') NOT NULL,
  `note` text,
  `transaction_date` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `stock_out_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.stock_out: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.stock_out_items
DROP TABLE IF EXISTS `stock_out_items`;
CREATE TABLE IF NOT EXISTS `stock_out_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `stock_out_id` int NOT NULL,
  `material_id` int NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `stock_out_id` (`stock_out_id`),
  KEY `material_id` (`material_id`),
  CONSTRAINT `stock_out_items_ibfk_1` FOREIGN KEY (`stock_out_id`) REFERENCES `stock_out` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stock_out_items_ibfk_2` FOREIGN KEY (`material_id`) REFERENCES `materials` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.stock_out_items: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.suppliers
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE IF NOT EXISTS `suppliers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `contact_person` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text,
  `tax_code` varchar(20) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.suppliers: ~0 rows (approximately)
INSERT INTO `suppliers` (`id`, `name`, `contact_person`, `phone`, `email`, `address`, `tax_code`, `status`, `created_at`, `updated_at`) VALUES
	(1, 'Trung Nguyên Coffee', 'Phạm Văn D', '0901234567', '<EMAIL>', '123 Nguyễn Huệ, Q1, TP.HCM', NULL, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(2, 'Vinamilk', 'Lê Thị E', '0907654321', '<EMAIL>', '456 Lê Lợi, Q1, TP.HCM', NULL, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(3, 'Phúc Long Tea', 'Trần Văn F', '0909876543', '<EMAIL>', '789 Hai Bà Trưng, Q3, TP.HCM', NULL, 'active', '2025-06-08 07:08:21', '2025-06-08 07:08:21');

-- Dumping structure for table quanlycafe.support_messages
DROP TABLE IF EXISTS `support_messages`;
CREATE TABLE IF NOT EXISTS `support_messages` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ticket_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `message` text NOT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `ticket_id` (`ticket_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `support_messages_ibfk_1` FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `support_messages_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.support_messages: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.support_tickets
DROP TABLE IF EXISTS `support_tickets`;
CREATE TABLE IF NOT EXISTS `support_tickets` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `title` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `screenshot` varchar(255) DEFAULT NULL,
  `category` enum('error','feature_request','help','other') NOT NULL,
  `priority` enum('low','medium','high','critical') DEFAULT 'medium',
  `status` enum('open','in_progress','resolved','closed') DEFAULT 'open',
  `assigned_to` int DEFAULT NULL,
  `resolution` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `assigned_to` (`assigned_to`),
  CONSTRAINT `support_tickets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `support_tickets_ibfk_2` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.support_tickets: ~0 rows (approximately)

-- Dumping structure for table quanlycafe.tables
DROP TABLE IF EXISTS `tables`;
CREATE TABLE IF NOT EXISTS `tables` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `area_id` int DEFAULT NULL,
  `capacity` int DEFAULT '4',
  `status` enum('vacant','occupied','reserved','maintenance') DEFAULT 'vacant',
  `pos_x` float DEFAULT NULL,
  `pos_y` float DEFAULT NULL,
  `width` float DEFAULT '100',
  `height` float DEFAULT '100',
  `shape` enum('rectangle','circle','custom') DEFAULT 'rectangle',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `area_id` (`area_id`),
  CONSTRAINT `tables_ibfk_1` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.tables: ~10 rows (approximately)
INSERT INTO `tables` (`id`, `name`, `area_id`, `capacity`, `status`, `pos_x`, `pos_y`, `width`, `height`, `shape`, `created_at`, `updated_at`) VALUES
	(1, 'T01', 1, 4, 'vacant', 100, 100, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(2, 'T02', 1, 4, 'vacant', 250, 100, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(3, 'T03', 1, 4, 'vacant', 400, 100, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(4, 'T04', 1, 4, 'vacant', 100, 250, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(5, 'T05', 1, 6, 'vacant', 250, 250, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(6, 'T06', 1, 6, 'vacant', 400, 250, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(7, 'T07', 2, 2, 'vacant', 100, 400, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(8, 'T08', 2, 2, 'vacant', 250, 400, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(9, 'T09', 2, 4, 'vacant', 400, 400, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21'),
	(10, 'T10', 3, 8, 'vacant', 550, 250, 100, 100, 'rectangle', '2025-06-08 07:08:21', '2025-06-08 07:08:21');

-- Dumping structure for table quanlycafe.table_merges
DROP TABLE IF EXISTS `table_merges`;
CREATE TABLE IF NOT EXISTS `table_merges` (
  `id` int NOT NULL AUTO_INCREMENT,
  `main_table_id` int NOT NULL,
  `merged_table_ids` json NOT NULL,
  `merged_at` datetime NOT NULL,
  `split_at` datetime DEFAULT NULL,
  `status` enum('merged','split') DEFAULT 'merged',
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `main_table_id` (`main_table_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `table_merges_ibfk_1` FOREIGN KEY (`main_table_id`) REFERENCES `tables` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `table_merges_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.table_merges: ~0 rows (approximately)

-- Dumping structure for procedure quanlycafe.update_material_stock_after_order
DROP PROCEDURE IF EXISTS `update_material_stock_after_order`;
DELIMITER //
CREATE PROCEDURE `update_material_stock_after_order`(IN p_order_id INT)
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE v_menu_item_id INT;
    DECLARE v_quantity INT;
    
    -- Cursor for order items
    DECLARE cur CURSOR FOR 
        SELECT menu_item_id, quantity 
        FROM order_items 
        WHERE order_id = p_order_id AND status <> 'cancelled';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
    
    -- Create temporary stock out record
    INSERT INTO stock_out (reference_no, reason, transaction_date)
    VALUES (CONCAT('ORD-', p_order_id), 'production', NOW());
    
    SET @stock_out_id = LAST_INSERT_ID();
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_menu_item_id, v_quantity;
        
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Insert stock out items for each material used in menu item
        INSERT INTO stock_out_items (stock_out_id, material_id, quantity, unit_price, total_price)
        SELECT 
            @stock_out_id,
            mim.material_id,
            mim.quantity * v_quantity,
            m.cost_price,
            m.cost_price * (mim.quantity * v_quantity)
        FROM menu_item_materials mim
        JOIN materials m ON mim.material_id = m.id
        WHERE mim.menu_item_id = v_menu_item_id;
        
        -- Update materials current stock
        UPDATE materials m
        JOIN menu_item_materials mim ON m.id = mim.material_id
        SET m.current_stock = m.current_stock - (mim.quantity * v_quantity)
        WHERE mim.menu_item_id = v_menu_item_id;
        
    END LOOP;
    
    CLOSE cur;
    
END//
DELIMITER ;

-- Dumping structure for table quanlycafe.users
DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `role_id` int NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `status` enum('active','inactive','locked') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table quanlycafe.users: ~0 rows (approximately)
INSERT INTO `users` (`id`, `username`, `password`, `email`, `role_id`, `last_login`, `status`, `created_at`, `updated_at`) VALUES
	(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 1, NULL, 'active', '2025-06-08 07:08:20', '2025-06-08 07:08:20');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
