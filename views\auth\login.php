<!-- Login Form -->
<form method="POST" action="<?= url('login') ?>" id="loginForm">
    <?= csrf_field() ?>
    
    <!-- Username/Email Input -->
    <div class="input-group">
        <span class="input-group-text">
            <i class="fas fa-user"></i>
        </span>
        <input 
            type="text" 
            class="form-control <?= !empty(errors('username')) ? 'is-invalid' : '' ?>" 
            name="username" 
            placeholder="Tên đăng nhập hoặc email"
            value="<?= old('username') ?>"
            required
            autocomplete="username"
            autofocus
        >
    </div>
    
    <!-- Display username errors -->
    <?php if (!empty(errors('username'))): ?>
        <div class="text-danger mb-3">
            <?php foreach (errors('username') as $error): ?>
                <small><i class="fas fa-exclamation-circle me-1"></i><?= $error ?></small><br>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Password Input -->
    <div class="input-group">
        <span class="input-group-text">
            <i class="fas fa-lock"></i>
        </span>
        <input 
            type="password" 
            class="form-control <?= !empty(errors('password')) ? 'is-invalid' : '' ?>" 
            name="password" 
            placeholder="Mật khẩu"
            required
            autocomplete="current-password"
            id="passwordInput"
        >
        <span class="input-group-text" style="cursor: pointer;" onclick="togglePassword()">
            <i class="fas fa-eye" id="passwordToggle"></i>
        </span>
    </div>
    
    <!-- Display password errors -->
    <?php if (!empty(errors('password'))): ?>
        <div class="text-danger mb-3">
            <?php foreach (errors('password') as $error): ?>
                <small><i class="fas fa-exclamation-circle me-1"></i><?= $error ?></small><br>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Remember Me Checkbox -->
    <div class="form-check mb-3">
        <input 
            class="form-check-input" 
            type="checkbox" 
            name="remember" 
            id="rememberMe"
            value="1"
        >
        <label class="form-check-label" for="rememberMe">
            Ghi nhớ đăng nhập
        </label>
    </div>
    
    <!-- Submit Button -->
    <button type="submit" class="btn btn-primary" id="loginBtn">
        <span class="btn-text">Đăng nhập</span>
        <span class="loading">
            <i class="fas fa-spinner fa-spin"></i>
        </span>
    </button>
</form>

<!-- Forgot Password Link -->
<div class="forgot-password">
    <a href="<?= url('forgot-password') ?>">Quên mật khẩu?</a>
</div>

<!-- Demo Accounts Info -->
<div class="mt-4 p-3" style="background: rgba(102, 126, 234, 0.1); border-radius: 15px; border-left: 4px solid #667eea;">
    <h6 class="mb-2" style="color: #667eea;">
        <i class="fas fa-info-circle me-2"></i>Tài khoản demo
    </h6>
    <div class="row">
        <div class="col-md-6">
            <small class="text-muted">
                <strong>Quản trị viên:</strong><br>
                Tên đăng nhập: <code>admin</code><br>
                Mật khẩu: <code>password</code>
            </small>
        </div>
        <div class="col-md-6">
            <small class="text-muted">
                <strong>Thu ngân:</strong><br>
                Tên đăng nhập: <code>cashier</code><br>
                Mật khẩu: <code>password</code>
            </small>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
function togglePassword() {
    const passwordInput = document.getElementById('passwordInput');
    const passwordToggle = document.getElementById('passwordToggle');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordToggle.classList.remove('fa-eye');
        passwordToggle.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordToggle.classList.remove('fa-eye-slash');
        passwordToggle.classList.add('fa-eye');
    }
}

// Form validation and submission
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const username = document.querySelector('input[name="username"]').value.trim();
    const password = document.querySelector('input[name="password"]').value;
    
    // Basic validation
    if (!username) {
        e.preventDefault();
        alert('Vui lòng nhập tên đăng nhập hoặc email.');
        return;
    }
    
    if (!password) {
        e.preventDefault();
        alert('Vui lòng nhập mật khẩu.');
        return;
    }
    
    if (password.length < 6) {
        e.preventDefault();
        alert('Mật khẩu phải có ít nhất 6 ký tự.');
        return;
    }
});

// Auto-focus on first empty field
document.addEventListener('DOMContentLoaded', function() {
    const usernameInput = document.querySelector('input[name="username"]');
    const passwordInput = document.querySelector('input[name="password"]');
    
    if (!usernameInput.value) {
        usernameInput.focus();
    } else if (!passwordInput.value) {
        passwordInput.focus();
    }
});

// Handle Enter key press
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.getElementById('loginForm').submit();
    }
});

// Demo account quick login
function quickLogin(username, password) {
    document.querySelector('input[name="username"]').value = username;
    document.querySelector('input[name="password"]').value = password;
    document.getElementById('loginForm').submit();
}

// Add click handlers for demo accounts
document.addEventListener('DOMContentLoaded', function() {
    // Make demo account info clickable
    const demoInfo = document.querySelector('.mt-4.p-3');
    if (demoInfo) {
        demoInfo.style.cursor = 'pointer';
        demoInfo.title = 'Nhấp để đăng nhập nhanh';
        
        // Add click handler for admin demo
        const adminSection = demoInfo.querySelector('.col-md-6:first-child');
        adminSection.addEventListener('click', function() {
            if (confirm('Đăng nhập với tài khoản Quản trị viên?')) {
                quickLogin('admin', 'password');
            }
        });
        
        // Add click handler for cashier demo
        const cashierSection = demoInfo.querySelector('.col-md-6:last-child');
        cashierSection.addEventListener('click', function() {
            if (confirm('Đăng nhập với tài khoản Thu ngân?')) {
                quickLogin('cashier', 'password');
            }
        });
    }
});
</script>
