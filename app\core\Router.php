<?php
/**
 * Router Class
 * Quản Lý <PERSON> Phê - Coffee Shop Management System
 */

class Router {
    private $routes = [];
    private $currentRoute = null;
    
    public function get($uri, $controller, $method) {
        $this->addRoute('GET', $uri, $controller, $method);
    }
    
    public function post($uri, $controller, $method) {
        $this->addRoute('POST', $uri, $controller, $method);
    }
    
    public function put($uri, $controller, $method) {
        $this->addRoute('PUT', $uri, $controller, $method);
    }
    
    public function delete($uri, $controller, $method) {
        $this->addRoute('DELETE', $uri, $controller, $method);
    }
    
    private function addRoute($httpMethod, $uri, $controller, $method) {
        $this->routes[] = [
            'method' => $httpMethod,
            'uri' => $uri,
            'controller' => $controller,
            'action' => $method
        ];
    }
    
    public function resolve($url) {
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestUri = implode('/', $url);
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $requestMethod && $this->matchUri($route['uri'], $requestUri)) {
                $this->currentRoute = $route;
                return [
                    'controller' => $route['controller'],
                    'method' => $route['action'],
                    'params' => $this->extractParams($route['uri'], $requestUri)
                ];
            }
        }
        
        return null;
    }
    
    private function matchUri($routeUri, $requestUri) {
        // Convert route pattern to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routeUri);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $requestUri);
    }
    
    private function extractParams($routeUri, $requestUri) {
        $params = [];
        
        // Extract parameter names from route
        preg_match_all('/\{([^}]+)\}/', $routeUri, $paramNames);
        
        // Extract parameter values from request URI
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routeUri);
        $pattern = '#^' . $pattern . '$#';
        
        if (preg_match($pattern, $requestUri, $matches)) {
            array_shift($matches); // Remove full match
            
            // Combine parameter names with values
            if (!empty($paramNames[1])) {
                $params = array_combine($paramNames[1], $matches);
            } else {
                $params = $matches;
            }
        }
        
        return $params;
    }
    
    public function getCurrentRoute() {
        return $this->currentRoute;
    }
    
    public function url($name, $params = []) {
        // Find route by name (if we implement named routes)
        foreach ($this->routes as $route) {
            if (isset($route['name']) && $route['name'] === $name) {
                $uri = $route['uri'];
                
                // Replace parameters in URI
                foreach ($params as $key => $value) {
                    $uri = str_replace('{' . $key . '}', $value, $uri);
                }
                
                return BASE_URL . ltrim($uri, '/');
            }
        }
        
        return BASE_URL;
    }
    
    public function redirect($uri, $statusCode = 302) {
        http_response_code($statusCode);
        header('Location: ' . BASE_URL . ltrim($uri, '/'));
        exit;
    }
    
    public function back() {
        $referer = $_SERVER['HTTP_REFERER'] ?? BASE_URL;
        header('Location: ' . $referer);
        exit;
    }
}
