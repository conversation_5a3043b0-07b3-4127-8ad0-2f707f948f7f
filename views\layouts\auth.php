<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><?= $title ?? APP_NAME ?></title>
    
    <!-- CSRF Token -->
    <?= CSRFMiddleware::metaTag() ?>
    
    <!-- Favicon -->
    <link rel="icon" href="<?= asset('images/favicon.ico') ?>" type="image/x-icon">
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            margin: 20px;
        }
        
        .auth-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .auth-logo i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .auth-logo h2 {
            color: #333;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .auth-logo p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e0e0e0;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-2px);
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group-text {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            border-radius: 15px 0 0 15px;
            padding: 15px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .alert {
            border: none;
            border-radius: 15px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #51cf66, #40c057);
            color: white;
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .forgot-password a:hover {
            color: #5a6fd8;
        }
        
        .footer-text {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.85rem;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .auth-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @media (max-width: 576px) {
            .auth-container {
                margin: 10px;
                padding: 30px 20px;
            }
            
            .auth-logo i {
                font-size: 2.5rem;
            }
            
            .auth-logo h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <!-- Logo and Title -->
        <div class="auth-logo">
            <i class="fas fa-coffee"></i>
            <h2><?= APP_NAME ?></h2>
            <p>Hệ thống quản lý quán cà phê</p>
        </div>
        
        <!-- Flash Messages -->
        <?php if (Session::hasFlash('success')): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?= Session::getFlash('success') ?>
            </div>
        <?php endif; ?>
        
        <?php if (Session::hasFlash('error')): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?= Session::getFlash('error') ?>
            </div>
        <?php endif; ?>
        
        <?php if (Session::hasFlash('warning')): ?>
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= Session::getFlash('warning') ?>
            </div>
        <?php endif; ?>
        
        <?php if (Session::hasFlash('info')): ?>
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <?= Session::getFlash('info') ?>
            </div>
        <?php endif; ?>
        
        <!-- Page Content -->
        <?= $content ?>
        
        <!-- Footer -->
        <div class="footer-text">
            <p>&copy; <?= date('Y') ?> <?= APP_NAME ?>. Phiên bản <?= APP_VERSION ?></p>
        </div>
    </div>

    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <script>
        // Setup CSRF token for AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Form submission with loading state
        $('form').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const loading = submitBtn.find('.loading');
            const text = submitBtn.find('.btn-text');
            
            submitBtn.prop('disabled', true);
            loading.addClass('show');
            text.text('Đang xử lý...');
        });
    </script>
</body>
</html>
