<?php
/**
 * Authentication Middleware
 * Quản Lý Cà Phê - Coffee Shop Management System
 */

class AuthMiddleware {
    
    /**
     * Handle authentication check
     */
    public function handle() {
        // Check if user is authenticated
        if (!Auth::check()) {
            // Store intended URL for redirect after login
            if (!$this->isAjaxRequest()) {
                Session::set('intended_url', $this->getCurrentUrl());
            }
            
            // Set flash message
            Session::setFlash('error', 'Vui lòng đăng nhập để tiếp tục.');
            
            // Redirect based on request type
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Phiên đăng nhập đã hết hạn.',
                    'redirect' => url('login')
                ], 401);
            } else {
                redirect('login');
            }
        }
        
        // Update last activity
        Session::updateActivity();
        
        // Check session timeout
        if (Session::checkTimeout()) {
            Session::setFlash('error', 'Phiên đăng nhập đã hết hạn.');
            
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Phiên đăng nhập đã hết hạn.',
                    'redirect' => url('login')
                ], 401);
            } else {
                redirect('login');
            }
        }
    }
    
    /**
     * Check if request is AJAX
     */
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Get current URL
     */
    private function getCurrentUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    }
    
    /**
     * Send JSON response
     */
    private function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
