<?php
/**
 * Authentication Class
 * Quản Lý <PERSON> Phê - Coffee Shop Management System
 */

class Auth {
    
    /**
     * Attempt to authenticate user
     */
    public static function attempt($username, $password) {
        $db = Database::getInstance();
        
        // Find user by username or email
        $sql = "SELECT u.*, r.name as role_name, r.permissions 
                FROM users u 
                JOIN roles r ON u.role_id = r.id 
                WHERE (u.username = ? OR u.email = ?) AND u.status = 'active'";
        
        $user = $db->fetch($sql, [$username, $username]);
        
        if ($user && password_verify($password, $user['password'])) {
            // Update last login
            $db->execute("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
            
            // Set user session
            unset($user['password']); // Remove password from session
            Session::setUser($user);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if user is authenticated
     */
    public static function check() {
        if (Session::checkTimeout()) {
            return false;
        }
        
        return Session::isLoggedIn();
    }
    
    /**
     * Get current authenticated user
     */
    public static function user() {
        if (!self::check()) {
            return null;
        }
        
        return Session::getUser();
    }
    
    /**
     * Get current user ID
     */
    public static function id() {
        $user = self::user();
        return $user ? $user['id'] : null;
    }
    
    /**
     * Logout current user
     */
    public static function logout() {
        Session::logout();
    }
    
    /**
     * Check if user has specific permission
     */
    public static function hasPermission($permission) {
        $user = self::user();
        if (!$user) {
            return false;
        }
        
        // Admin has all permissions
        if ($user['role_name'] === 'Admin') {
            return true;
        }
        
        $permissions = json_decode($user['permissions'], true);
        if (!$permissions) {
            return false;
        }
        
        // Check for "all" permission
        if (isset($permissions['all']) && $permissions['all']) {
            return true;
        }
        
        // Check specific permission
        return self::checkPermission($permissions, $permission);
    }
    
    /**
     * Check permission recursively
     */
    private static function checkPermission($permissions, $permission) {
        $parts = explode('.', $permission);
        $current = $permissions;
        
        foreach ($parts as $part) {
            if (!isset($current[$part])) {
                return false;
            }
            
            if (is_bool($current[$part])) {
                return $current[$part];
            }
            
            $current = $current[$part];
        }
        
        return is_bool($current) ? $current : false;
    }
    
    /**
     * Check if user has any of the given permissions
     */
    public static function hasAnyPermission($permissions) {
        foreach ($permissions as $permission) {
            if (self::hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if user has all of the given permissions
     */
    public static function hasAllPermissions($permissions) {
        foreach ($permissions as $permission) {
            if (!self::hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Check if user has specific role
     */
    public static function hasRole($role) {
        $user = self::user();
        return $user && $user['role_name'] === $role;
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin() {
        return self::hasRole('Admin');
    }
    
    /**
     * Check if user is manager
     */
    public static function isManager() {
        return self::hasRole('Manager');
    }
    
    /**
     * Get user role
     */
    public static function getRole() {
        $user = self::user();
        return $user ? $user['role_name'] : null;
    }
    
    /**
     * Get user permissions
     */
    public static function getPermissions() {
        $user = self::user();
        if (!$user) {
            return [];
        }
        
        return json_decode($user['permissions'], true) ?: [];
    }
    
    /**
     * Require authentication
     */
    public static function requireAuth() {
        if (!self::check()) {
            Session::setFlash('error', 'Vui lòng đăng nhập để tiếp tục.');
            redirect('login');
        }
    }
    
    /**
     * Require specific permission
     */
    public static function requirePermission($permission) {
        self::requireAuth();
        
        if (!self::hasPermission($permission)) {
            Session::setFlash('error', 'Bạn không có quyền truy cập chức năng này.');
            redirect('dashboard');
        }
    }
    
    /**
     * Require specific role
     */
    public static function requireRole($role) {
        self::requireAuth();
        
        if (!self::hasRole($role)) {
            Session::setFlash('error', 'Bạn không có quyền truy cập chức năng này.');
            redirect('dashboard');
        }
    }
    
    /**
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, HASH_ALGO);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate random password
     */
    public static function generatePassword($length = 8) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        return substr(str_shuffle($chars), 0, $length);
    }
    
    /**
     * Check if password is strong enough
     */
    public static function isStrongPassword($password) {
        return strlen($password) >= 8 && 
               preg_match('/[A-Z]/', $password) && 
               preg_match('/[a-z]/', $password) && 
               preg_match('/[0-9]/', $password);
    }
}
