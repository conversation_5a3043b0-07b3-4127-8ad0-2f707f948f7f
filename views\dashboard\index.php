<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="fas fa-tachometer-alt me-2 text-primary"></i>
            Tổng quan hệ thống
        </h1>
        <p class="text-muted mb-0">Chào mừng trở lại, <?= Auth::user()['username'] ?>!</p>
    </div>
    <div class="text-end">
        <small class="text-muted">
            <i class="fas fa-clock me-1"></i>
            Cập nhật lúc: <?= date('d/m/Y H:i') ?>
        </small>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <!-- Today's Orders -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Đơn hàng hôm nay
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['orders_today']) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Revenue -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Doanh thu hôm nay
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= formatCurrency($stats['revenue_today']) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Tables -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Bàn đang sử dụng
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['active_tables'] ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-table fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Alert -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Cảnh báo tồn kho
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['low_stock_count'] ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
    <!-- Revenue Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-area me-2"></i>
                    Biểu đồ doanh thu
                </h6>
                <div class="dropdown no-arrow">
                    <select class="form-select form-select-sm" id="revenueChartPeriod">
                        <option value="week">7 ngày qua</option>
                        <option value="month" selected>30 ngày qua</option>
                        <option value="year">12 tháng qua</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" width="100%" height="40"></canvas>
            </div>
        </div>
    </div>

    <!-- Best Selling Items -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-star me-2"></i>
                    Món bán chạy
                </h6>
            </div>
            <div class="card-body">
                <div id="bestSellingItems">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i>
                        Đang tải...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders and Reservations -->
<div class="row">
    <!-- Recent Orders -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    Đơn hàng gần đây
                </h6>
                <a href="<?= url('orders') ?>" class="btn btn-sm btn-outline-primary">
                    Xem tất cả
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentOrders)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Mã đơn</th>
                                    <th>Bàn</th>
                                    <th>Tổng tiền</th>
                                    <th>Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentOrders as $order): ?>
                                    <tr>
                                        <td>
                                            <small class="font-weight-bold">
                                                <?= $order['order_number'] ?? '#' . $order['id'] ?>
                                            </small>
                                        </td>
                                        <td>
                                            <small><?= $order['table_name'] ?? 'Mang về' ?></small>
                                        </td>
                                        <td>
                                            <small class="text-success font-weight-bold">
                                                <?= formatCurrency($order['total_amount']) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = match($order['status']) {
                                                'pending' => 'warning',
                                                'confirmed' => 'info',
                                                'preparing' => 'primary',
                                                'ready' => 'success',
                                                'completed' => 'success',
                                                'cancelled' => 'danger',
                                                default => 'secondary'
                                            };
                                            $statusText = match($order['status']) {
                                                'pending' => 'Chờ xử lý',
                                                'confirmed' => 'Đã xác nhận',
                                                'preparing' => 'Đang chuẩn bị',
                                                'ready' => 'Sẵn sàng',
                                                'completed' => 'Hoàn thành',
                                                'cancelled' => 'Đã hủy',
                                                default => $order['status']
                                            };
                                            ?>
                                            <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>Chưa có đơn hàng nào</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Low Stock Items -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Cảnh báo tồn kho
                </h6>
                <a href="<?= url('inventory') ?>" class="btn btn-sm btn-outline-warning">
                    Xem kho hàng
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($lowStockItems)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Nguyên liệu</th>
                                    <th>Tồn kho</th>
                                    <th>Tối thiểu</th>
                                    <th>Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lowStockItems as $item): ?>
                                    <tr>
                                        <td>
                                            <small class="font-weight-bold"><?= $item['name'] ?></small>
                                            <br>
                                            <small class="text-muted"><?= $item['category_name'] ?></small>
                                        </td>
                                        <td>
                                            <small><?= number_format($item['current_stock'], 2) ?> <?= $item['unit'] ?></small>
                                        </td>
                                        <td>
                                            <small><?= number_format($item['min_stock_level'], 2) ?> <?= $item['unit'] ?></small>
                                        </td>
                                        <td>
                                            <?php if ($item['stock_status'] === 'out_of_stock'): ?>
                                                <span class="badge bg-danger">Hết hàng</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Sắp hết</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                        <p>Tất cả nguyên liệu đều đủ</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Revenue Chart
let revenueChart;

function loadRevenueChart(period = 'month') {
    $.ajax({
        url: '<?= url('dashboard/revenue-chart') ?>',
        method: 'GET',
        data: { period: period },
        success: function(response) {
            if (response.success) {
                updateRevenueChart(response.data);
            }
        },
        error: function() {
            console.error('Failed to load revenue chart data');
        }
    });
}

function updateRevenueChart(data) {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    
    if (revenueChart) {
        revenueChart.destroy();
    }
    
    const labels = data.map(item => {
        const date = new Date(item.date);
        return date.toLocaleDateString('vi-VN');
    });
    
    const revenues = data.map(item => parseFloat(item.revenue));
    
    revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Doanh thu (VNĐ)',
                data: revenues,
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('vi-VN').format(value) + ' ₫';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Doanh thu: ' + new Intl.NumberFormat('vi-VN').format(context.parsed.y) + ' ₫';
                        }
                    }
                }
            }
        }
    });
}

// Best Selling Items
function loadBestSellingItems() {
    $.ajax({
        url: '<?= url('dashboard/best-selling') ?>',
        method: 'GET',
        data: { period: 'month', limit: 5 },
        success: function(response) {
            if (response.success) {
                updateBestSellingItems(response.data);
            }
        },
        error: function() {
            $('#bestSellingItems').html('<div class="text-center text-danger">Không thể tải dữ liệu</div>');
        }
    });
}

function updateBestSellingItems(data) {
    let html = '';
    
    if (data.length > 0) {
        data.forEach((item, index) => {
            html += `
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <span class="badge bg-primary rounded-pill">${index + 1}</span>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${item.name}</div>
                        <small class="text-muted">${item.category}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-success">${parseInt(item.total_quantity)}</div>
                        <small class="text-muted">đã bán</small>
                    </div>
                </div>
            `;
        });
    } else {
        html = '<div class="text-center text-muted">Chưa có dữ liệu</div>';
    }
    
    $('#bestSellingItems').html(html);
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Load initial data
    loadRevenueChart();
    loadBestSellingItems();
    
    // Revenue chart period change
    $('#revenueChartPeriod').on('change', function() {
        loadRevenueChart(this.value);
    });
    
    // Auto refresh every 5 minutes
    setInterval(function() {
        loadRevenueChart($('#revenueChartPeriod').val());
        loadBestSellingItems();
    }, 300000);
});
</script>
