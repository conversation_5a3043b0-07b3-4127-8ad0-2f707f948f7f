/**
 * Custom CSS for Quản Lý <PERSON> Phê
 * Coffee Shop Management System
 */

/* Root Variables */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
    padding: 1rem 1.5rem;
}

.card-header h6 {
    margin: 0;
    font-weight: 600;
}

/* Button Styles */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #20c997);
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #e74c3c);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #f39c12);
}

.btn-info {
    background: linear-gradient(45deg, var(--info-color), #3498db);
}

/* Form Controls */
.form-control {
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    padding: 12px 15px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.form-select {
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    padding: 12px 15px;
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 500;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

/* Badge Styles */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 15px 20px;
}

.alert-success {
    background: linear-gradient(45deg, var(--success-color), #20c997);
    color: white;
}

.alert-danger {
    background: linear-gradient(45deg, var(--danger-color), #e74c3c);
    color: white;
}

.alert-warning {
    background: linear-gradient(45deg, var(--warning-color), #f39c12);
    color: white;
}

.alert-info {
    background: linear-gradient(45deg, var(--info-color), #3498db);
    color: white;
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    box-shadow: var(--box-shadow);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    width: 250px;
    padding-top: 80px;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    border-radius: 8px;
    margin: 2px 10px;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 20px;
    padding: 30px;
    min-height: calc(100vh - 140px);
}

/* Navbar Styles */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    background: white !important;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    height: 70px;
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
    font-size: 1.5rem;
}

/* Content with sidebar offset */
.content-with-sidebar {
    margin-left: 250px;
    padding-top: 70px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .content-with-sidebar {
        margin-left: 0;
    }
    
    .main-content {
        margin: 10px;
        padding: 20px;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--danger-color);
}

.status-indicator.busy {
    background-color: var(--warning-color);
}

/* Custom Utilities */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-left-info {
    border-left: 4px solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
        box-shadow: none;
        border-radius: 0;
    }
    
    .content-with-sidebar {
        margin-left: 0;
        padding-top: 0;
    }
}
