<?php
/**
 * Application Configuration
 * Quản <PERSON> - Coffee Shop Management System
 */

// Environment settings
define('ENVIRONMENT', 'development'); // development, production
define('BASE_URL', 'http://localhost/quanlycafe/');
define('APP_NAME', 'Quản <PERSON>ê');
define('APP_VERSION', '1.0.0');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'quanlycafe');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Application paths
define('ROOT_PATH', dirname(__DIR__) . '/');
define('APP_PATH', ROOT_PATH . 'app/');
define('VIEW_PATH', ROOT_PATH . 'views/');
define('PUBLIC_PATH', ROOT_PATH . 'public/');
define('UPLOAD_PATH', PUBLIC_PATH . 'uploads/');

// Security settings
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600); // 1 hour
define('CSRF_TOKEN_NAME', '_token');

// Application settings
define('DEFAULT_CONTROLLER', 'Dashboard');
define('DEFAULT_METHOD', 'index');
define('ITEMS_PER_PAGE', 20);

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx']);

// Email settings (for notifications)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Quản Lý Cà Phê');

// Currency and locale settings
define('CURRENCY_SYMBOL', '₫');
define('CURRENCY_CODE', 'VND');
define('LOCALE', 'vi_VN');
define('DATE_FORMAT', 'd/m/Y');
define('DATETIME_FORMAT', 'd/m/Y H:i');
define('TIME_FORMAT', 'H:i');

// Tax settings
define('DEFAULT_TAX_RATE', 0.1); // 10% VAT
define('TAX_INCLUDED', true);

// Business settings
define('BUSINESS_NAME', 'Quán Cà Phê ABC');
define('BUSINESS_ADDRESS', '123 Nguyễn Huệ, Quận 1, TP.HCM');
define('BUSINESS_PHONE', '028 1234 5678');
define('BUSINESS_EMAIL', '<EMAIL>');

// Error reporting
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', ROOT_PATH . 'logs/error.log');
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', ROOT_PATH . 'logs/error.log');
}

// Create necessary directories
$directories = [
    ROOT_PATH . 'logs',
    UPLOAD_PATH,
    UPLOAD_PATH . 'images',
    UPLOAD_PATH . 'documents',
    UPLOAD_PATH . 'avatars'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
