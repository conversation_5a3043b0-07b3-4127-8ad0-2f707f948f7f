<?php
/**
 * Main Application Class
 * Quản Lý Cà Phê - Coffee Shop Management System
 */

class App {
    private $router;
    private $controller;
    private $method;
    private $params = [];
    
    public function __construct() {
        $this->router = new Router();
        $this->setupRoutes();
    }
    
    public function run() {
        // Parse URL
        $url = $this->parseUrl();
        
        // Get route info
        $route = $this->router->resolve($url);
        
        if ($route) {
            $this->controller = $route['controller'];
            $this->method = $route['method'];
            $this->params = $route['params'];
        } else {
            // Default routing
            $this->controller = $url[0] ?? DEFAULT_CONTROLLER;
            $this->method = $url[1] ?? DEFAULT_METHOD;
            $this->params = array_slice($url, 2);
        }
        
        // Load controller
        $this->loadController();
    }
    
    private function parseUrl() {
        if (isset($_GET['url'])) {
            $url = rtrim($_GET['url'], '/');
            $url = filter_var($url, FILTER_SANITIZE_URL);
            return explode('/', $url);
        }
        return [];
    }
    
    private function loadController() {
        // Sanitize controller name
        $this->controller = ucfirst(strtolower($this->controller));
        $controllerFile = 'app/controllers/' . $this->controller . 'Controller.php';
        
        if (file_exists($controllerFile)) {
            require_once $controllerFile;
            $controllerClass = $this->controller . 'Controller';
            
            if (class_exists($controllerClass)) {
                $this->controller = new $controllerClass();
                
                // Check if method exists
                if (method_exists($this->controller, $this->method)) {
                    // Apply middleware if defined
                    $this->applyMiddleware();
                    
                    // Call the method with parameters
                    call_user_func_array([$this->controller, $this->method], $this->params);
                } else {
                    $this->show404();
                }
            } else {
                $this->show404();
            }
        } else {
            $this->show404();
        }
    }
    
    private function applyMiddleware() {
        // Check if controller has middleware defined
        if (property_exists($this->controller, 'middleware')) {
            foreach ($this->controller->middleware as $middleware) {
                $middlewareClass = $middleware . 'Middleware';
                if (class_exists($middlewareClass)) {
                    $middlewareInstance = new $middlewareClass();
                    if (method_exists($middlewareInstance, 'handle')) {
                        $middlewareInstance->handle();
                    }
                }
            }
        }
    }
    
    private function setupRoutes() {
        // Authentication routes
        $this->router->get('login', 'Auth', 'login');
        $this->router->post('login', 'Auth', 'authenticate');
        $this->router->get('logout', 'Auth', 'logout');
        
        // Dashboard
        $this->router->get('', 'Dashboard', 'index');
        $this->router->get('dashboard', 'Dashboard', 'index');
        
        // Table management
        $this->router->get('tables', 'Tables', 'index');
        $this->router->get('tables/layout', 'Tables', 'layout');
        $this->router->post('tables/update-status', 'Tables', 'updateStatus');
        
        // Menu management
        $this->router->get('menu', 'Menu', 'index');
        $this->router->get('menu/categories', 'Menu', 'categories');
        $this->router->get('menu/items', 'Menu', 'items');
        
        // Orders
        $this->router->get('orders', 'Orders', 'index');
        $this->router->get('orders/create', 'Orders', 'create');
        $this->router->post('orders/store', 'Orders', 'store');
        
        // Inventory
        $this->router->get('inventory', 'Inventory', 'index');
        $this->router->get('inventory/materials', 'Inventory', 'materials');
        $this->router->get('inventory/stock-in', 'Inventory', 'stockIn');
        $this->router->get('inventory/stock-out', 'Inventory', 'stockOut');
        
        // Employees
        $this->router->get('employees', 'Employees', 'index');
        $this->router->get('employees/shifts', 'Employees', 'shifts');
        $this->router->get('employees/attendance', 'Employees', 'attendance');
        
        // Reports
        $this->router->get('reports', 'Reports', 'index');
        $this->router->get('reports/revenue', 'Reports', 'revenue');
        $this->router->get('reports/bestselling', 'Reports', 'bestSelling');
        
        // Support
        $this->router->get('support', 'Support', 'index');
        $this->router->get('support/tickets', 'Support', 'tickets');
        $this->router->get('support/faq', 'Support', 'faq');
        
        // API routes
        $this->router->get('api/tables/status', 'Api\Tables', 'getStatus');
        $this->router->post('api/orders/add-item', 'Api\Orders', 'addItem');
        $this->router->get('api/dashboard/stats', 'Api\Dashboard', 'getStats');
    }
    
    private function show404() {
        http_response_code(404);
        if (file_exists('views/errors/404.php')) {
            require_once 'views/errors/404.php';
        } else {
            echo '<h1>404 - Trang không tìm thấy</h1>';
            echo '<p>Trang bạn đang tìm kiếm không tồn tại.</p>';
            echo '<a href="' . BASE_URL . '">Về trang chủ</a>';
        }
        exit;
    }
}
