<?php
/**
 * Role-based Access Control Middleware
 * Quản Lý Cà Phê - Coffee Shop Management System
 */

class RoleMiddleware {
    private $requiredPermissions = [];
    private $requiredRoles = [];
    
    /**
     * Set required permissions
     */
    public function requirePermissions($permissions) {
        $this->requiredPermissions = is_array($permissions) ? $permissions : [$permissions];
        return $this;
    }
    
    /**
     * Set required roles
     */
    public function requireRoles($roles) {
        $this->requiredRoles = is_array($roles) ? $roles : [$roles];
        return $this;
    }
    
    /**
     * Handle role and permission check
     */
    public function handle() {
        // First ensure user is authenticated
        if (!Auth::check()) {
            $this->handleUnauthorized('Vui lòng đăng nhập để tiếp tục.');
            return;
        }
        
        // Check required roles
        if (!empty($this->requiredRoles)) {
            $hasRole = false;
            foreach ($this->requiredRoles as $role) {
                if (Auth::hasRole($role)) {
                    $hasRole = true;
                    break;
                }
            }
            
            if (!$hasRole) {
                $this->handleForbidden('Bạn không có quyền truy cập chức năng này.');
                return;
            }
        }
        
        // Check required permissions
        if (!empty($this->requiredPermissions)) {
            $hasPermission = false;
            foreach ($this->requiredPermissions as $permission) {
                if (Auth::hasPermission($permission)) {
                    $hasPermission = true;
                    break;
                }
            }
            
            if (!$hasPermission) {
                $this->handleForbidden('Bạn không có quyền truy cập chức năng này.');
                return;
            }
        }
        
        // Log access attempt
        $this->logAccess();
    }
    
    /**
     * Handle unauthorized access (not logged in)
     */
    private function handleUnauthorized($message) {
        Session::setFlash('error', $message);
        
        if ($this->isAjaxRequest()) {
            $this->jsonResponse([
                'success' => false,
                'message' => $message,
                'redirect' => url('login')
            ], 401);
        } else {
            redirect('login');
        }
    }
    
    /**
     * Handle forbidden access (insufficient permissions)
     */
    private function handleForbidden($message) {
        Session::setFlash('error', $message);
        
        if ($this->isAjaxRequest()) {
            $this->jsonResponse([
                'success' => false,
                'message' => $message,
                'redirect' => url('dashboard')
            ], 403);
        } else {
            redirect('dashboard');
        }
    }
    
    /**
     * Check if request is AJAX
     */
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Send JSON response
     */
    private function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Log access attempt
     */
    private function logAccess() {
        $user = Auth::user();
        $logData = [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role_name'],
            'url' => $_SERVER['REQUEST_URI'],
            'method' => $_SERVER['REQUEST_METHOD'],
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log to file or database as needed
        error_log('Access: ' . json_encode($logData));
    }
    
    /**
     * Static method to create middleware with permissions
     */
    public static function permissions($permissions) {
        $middleware = new self();
        return $middleware->requirePermissions($permissions);
    }
    
    /**
     * Static method to create middleware with roles
     */
    public static function roles($roles) {
        $middleware = new self();
        return $middleware->requireRoles($roles);
    }
    
    /**
     * Check if user can access specific module
     */
    public static function canAccess($module, $action = 'view') {
        if (!Auth::check()) {
            return false;
        }
        
        // Admin can access everything
        if (Auth::isAdmin()) {
            return true;
        }
        
        // Check specific permission
        $permission = $module . '.' . $action;
        return Auth::hasPermission($permission) || Auth::hasPermission($module);
    }
    
    /**
     * Get accessible modules for current user
     */
    public static function getAccessibleModules() {
        if (!Auth::check()) {
            return [];
        }
        
        $user = Auth::user();
        $permissions = json_decode($user['permissions'], true) ?: [];
        
        // Admin has access to all modules
        if (Auth::isAdmin() || isset($permissions['all'])) {
            return [
                'dashboard', 'tables', 'menu', 'orders', 'inventory', 
                'employees', 'reports', 'support', 'settings'
            ];
        }
        
        // Return modules based on permissions
        return array_keys($permissions);
    }
}
