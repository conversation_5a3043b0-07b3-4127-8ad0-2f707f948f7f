<?php
/**
 * Dashboard Controller
 * Quản <PERSON>ê - Coffee Shop Management System
 */

class DashboardController extends Controller {
    protected $middleware = ['Auth'];
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Dashboard index page
     */
    public function index() {
        // Get dashboard statistics
        $stats = $this->getDashboardStats();
        
        // Get recent activities
        $recentOrders = $this->getRecentOrders();
        $recentReservations = $this->getRecentReservations();
        
        // Get low stock alerts
        $lowStockItems = $this->getLowStockItems();
        
        // Get today's revenue
        $todayRevenue = $this->getTodayRevenue();
        
        // Get table status summary
        $tableStatus = $this->getTableStatusSummary();
        
        $this->view('dashboard/index', [
            'title' => 'Tổng quan - ' . APP_NAME,
            'activeMenu' => 'dashboard',
            'stats' => $stats,
            'recentOrders' => $recentOrders,
            'recentReservations' => $recentReservations,
            'lowStockItems' => $lowStockItems,
            'todayRevenue' => $todayRevenue,
            'tableStatus' => $tableStatus
        ]);
    }
    
    /**
     * Get dashboard statistics
     */
    private function getDashboardStats() {
        $db = Database::getInstance();
        
        // Today's statistics
        $today = date('Y-m-d');
        
        // Orders today
        $ordersToday = $db->fetch(
            "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total 
             FROM orders 
             WHERE DATE(ordered_at) = ? AND status != 'cancelled'",
            [$today]
        );
        
        // Revenue this month
        $thisMonth = date('Y-m');
        $revenueThisMonth = $db->fetch(
            "SELECT COALESCE(SUM(total_amount), 0) as total 
             FROM invoices 
             WHERE DATE_FORMAT(issued_at, '%Y-%m') = ? AND payment_status = 'paid'",
            [$thisMonth]
        );
        
        // Active tables
        $activeTables = $db->fetch(
            "SELECT COUNT(*) as count FROM tables WHERE status = 'occupied'"
        );
        
        // Total customers
        $totalCustomers = $db->fetch(
            "SELECT COUNT(*) as count FROM customers"
        );
        
        // Low stock items
        $lowStockCount = $db->fetch(
            "SELECT COUNT(*) as count 
             FROM materials 
             WHERE current_stock <= min_stock_level AND status = 'active'"
        );
        
        // Pending orders
        $pendingOrders = $db->fetch(
            "SELECT COUNT(*) as count 
             FROM orders 
             WHERE status IN ('pending', 'confirmed', 'preparing')"
        );
        
        return [
            'orders_today' => $ordersToday['count'] ?? 0,
            'revenue_today' => $ordersToday['total'] ?? 0,
            'revenue_month' => $revenueThisMonth['total'] ?? 0,
            'active_tables' => $activeTables['count'] ?? 0,
            'total_customers' => $totalCustomers['count'] ?? 0,
            'low_stock_count' => $lowStockCount['count'] ?? 0,
            'pending_orders' => $pendingOrders['count'] ?? 0
        ];
    }
    
    /**
     * Get recent orders
     */
    private function getRecentOrders($limit = 5) {
        $db = Database::getInstance();
        
        return $db->fetchAll(
            "SELECT o.*, t.name as table_name, c.name as customer_name
             FROM orders o
             LEFT JOIN tables t ON o.table_id = t.id
             LEFT JOIN customers c ON o.customer_id = c.id
             ORDER BY o.ordered_at DESC
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get recent reservations
     */
    private function getRecentReservations($limit = 5) {
        $db = Database::getInstance();
        
        return $db->fetchAll(
            "SELECT r.*, t.name as table_name
             FROM reservations r
             LEFT JOIN tables t ON r.table_id = t.id
             WHERE r.reservation_date >= CURDATE()
             ORDER BY r.reservation_date ASC, r.start_time ASC
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get low stock items
     */
    private function getLowStockItems($limit = 10) {
        $db = Database::getInstance();
        
        return $db->fetchAll(
            "SELECT m.*, mc.name as category_name,
                    CASE 
                        WHEN m.current_stock = 0 THEN 'out_of_stock'
                        WHEN m.current_stock <= m.min_stock_level THEN 'low_stock'
                        ELSE 'normal'
                    END as stock_status
             FROM materials m
             LEFT JOIN material_categories mc ON m.category_id = mc.id
             WHERE m.current_stock <= m.min_stock_level AND m.status = 'active'
             ORDER BY m.current_stock ASC
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get today's revenue breakdown
     */
    private function getTodayRevenue() {
        $db = Database::getInstance();
        $today = date('Y-m-d');
        
        $revenue = $db->fetch(
            "SELECT 
                COALESCE(SUM(i.total_amount), 0) as total_revenue,
                COALESCE(SUM(i.subtotal), 0) as subtotal,
                COALESCE(SUM(i.tax_amount), 0) as tax_amount,
                COALESCE(SUM(i.discount_amount), 0) as discount_amount,
                COUNT(i.id) as invoice_count
             FROM invoices i
             WHERE DATE(i.issued_at) = ? AND i.payment_status = 'paid'",
            [$today]
        );
        
        // Get payment method breakdown
        $paymentMethods = $db->fetchAll(
            "SELECT 
                p.payment_method,
                COALESCE(SUM(p.amount), 0) as amount,
                COUNT(p.id) as count
             FROM payments p
             JOIN invoices i ON p.invoice_id = i.id
             WHERE DATE(i.issued_at) = ? AND p.status = 'completed'
             GROUP BY p.payment_method",
            [$today]
        );
        
        return [
            'summary' => $revenue,
            'payment_methods' => $paymentMethods
        ];
    }
    
    /**
     * Get table status summary
     */
    private function getTableStatusSummary() {
        $db = Database::getInstance();
        
        return $db->fetchAll(
            "SELECT 
                status,
                COUNT(*) as count
             FROM tables
             GROUP BY status"
        );
    }
    
    /**
     * Get revenue chart data (AJAX)
     */
    public function getRevenueChart() {
        if (!$this->isAjax()) {
            $this->json(['error' => 'Invalid request'], 400);
        }
        
        $period = $this->input('period', 'week'); // week, month, year
        $db = Database::getInstance();
        
        switch ($period) {
            case 'week':
                $data = $db->fetchAll(
                    "SELECT 
                        DATE(issued_at) as date,
                        COALESCE(SUM(total_amount), 0) as revenue
                     FROM invoices
                     WHERE issued_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                       AND payment_status = 'paid'
                     GROUP BY DATE(issued_at)
                     ORDER BY date ASC"
                );
                break;
                
            case 'month':
                $data = $db->fetchAll(
                    "SELECT 
                        DATE(issued_at) as date,
                        COALESCE(SUM(total_amount), 0) as revenue
                     FROM invoices
                     WHERE issued_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                       AND payment_status = 'paid'
                     GROUP BY DATE(issued_at)
                     ORDER BY date ASC"
                );
                break;
                
            case 'year':
                $data = $db->fetchAll(
                    "SELECT 
                        DATE_FORMAT(issued_at, '%Y-%m') as date,
                        COALESCE(SUM(total_amount), 0) as revenue
                     FROM invoices
                     WHERE issued_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                       AND payment_status = 'paid'
                     GROUP BY DATE_FORMAT(issued_at, '%Y-%m')
                     ORDER BY date ASC"
                );
                break;
                
            default:
                $data = [];
        }
        
        $this->json([
            'success' => true,
            'data' => $data
        ]);
    }
    
    /**
     * Get best selling items (AJAX)
     */
    public function getBestSellingItems() {
        if (!$this->isAjax()) {
            $this->json(['error' => 'Invalid request'], 400);
        }
        
        $period = $this->input('period', 'month');
        $limit = $this->input('limit', 10);
        
        $db = Database::getInstance();
        
        $dateCondition = match($period) {
            'today' => "DATE(o.ordered_at) = CURDATE()",
            'week' => "o.ordered_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)",
            'month' => "o.ordered_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)",
            'year' => "o.ordered_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)",
            default => "o.ordered_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)"
        };
        
        $data = $db->fetchAll(
            "SELECT 
                mi.name,
                mc.name as category,
                SUM(oi.quantity) as total_quantity,
                COALESCE(SUM(oi.total_price), 0) as total_revenue
             FROM order_items oi
             JOIN menu_items mi ON oi.menu_item_id = mi.id
             JOIN menu_categories mc ON mi.category_id = mc.id
             JOIN orders o ON oi.order_id = o.id
             WHERE $dateCondition AND o.status = 'completed'
             GROUP BY mi.id, mi.name, mc.name
             ORDER BY total_quantity DESC
             LIMIT ?",
            [$limit]
        );
        
        $this->json([
            'success' => true,
            'data' => $data
        ]);
    }
}
