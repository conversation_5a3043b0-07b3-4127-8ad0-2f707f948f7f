<?php
/**
 * Base Controller Class
 * Quản <PERSON>ê - Coffee Shop Management System
 */

class Controller {
    protected $view;
    protected $model;
    protected $middleware = [];
    
    public function __construct() {
        $this->view = new View();
    }
    
    /**
     * Load a model
     */
    protected function model($model) {
        $modelFile = 'app/models/' . $model . '.php';
        if (file_exists($modelFile)) {
            require_once $modelFile;
            return new $model();
        }
        throw new Exception("Model {$model} not found");
    }
    
    /**
     * Render a view
     */
    protected function view($view, $data = []) {
        $this->view->render($view, $data);
    }
    
    /**
     * Return JSON response
     */
    protected function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Redirect to another URL
     */
    protected function redirect($url, $statusCode = 302) {
        if (strpos($url, 'http') !== 0) {
            $url = BASE_URL . ltrim($url, '/');
        }
        http_response_code($statusCode);
        header("Location: $url");
        exit;
    }
    
    /**
     * Redirect back to previous page
     */
    protected function back() {
        $referer = $_SERVER['HTTP_REFERER'] ?? BASE_URL;
        header("Location: $referer");
        exit;
    }
    
    /**
     * Validate request data
     */
    protected function validate($data, $rules) {
        $validator = new Validator();
        return $validator->validate($data, $rules);
    }
    
    /**
     * Get request input
     */
    protected function input($key = null, $default = null) {
        if ($key === null) {
            return $_REQUEST;
        }
        return $_REQUEST[$key] ?? $default;
    }
    
    /**
     * Check if request is POST
     */
    protected function isPost() {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }
    
    /**
     * Check if request is GET
     */
    protected function isGet() {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }
    
    /**
     * Check if request is AJAX
     */
    protected function isAjax() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Get uploaded file
     */
    protected function file($key) {
        return $_FILES[$key] ?? null;
    }
    
    /**
     * Set flash message
     */
    protected function setFlash($type, $message) {
        Session::setFlash($type, $message);
    }
    
    /**
     * Set success message
     */
    protected function setSuccess($message) {
        Session::setFlash('success', $message);
    }
    
    /**
     * Set error message
     */
    protected function setError($message) {
        Session::setFlash('error', $message);
    }
    
    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated() {
        return Auth::check();
    }
    
    /**
     * Get current user
     */
    protected function user() {
        return Auth::user();
    }
    
    /**
     * Check if user has permission
     */
    protected function hasPermission($permission) {
        return Auth::hasPermission($permission);
    }
    
    /**
     * Require authentication
     */
    protected function requireAuth() {
        if (!$this->isAuthenticated()) {
            $this->redirect('login');
        }
    }
    
    /**
     * Require specific permission
     */
    protected function requirePermission($permission) {
        $this->requireAuth();
        if (!$this->hasPermission($permission)) {
            $this->setError('Bạn không có quyền truy cập chức năng này.');
            $this->redirect('dashboard');
        }
    }
    
    /**
     * Verify CSRF token
     */
    protected function verifyCsrf() {
        $token = $this->input(CSRF_TOKEN_NAME);
        if (!Session::verifyCsrfToken($token)) {
            $this->setError('Token bảo mật không hợp lệ.');
            $this->back();
        }
    }
    
    /**
     * Handle file upload
     */
    protected function uploadFile($file, $directory = 'uploads', $allowedTypes = null) {
        if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
            return false;
        }
        
        $allowedTypes = $allowedTypes ?? ALLOWED_IMAGE_TYPES;
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($fileExtension, $allowedTypes)) {
            throw new Exception('Loại file không được phép.');
        }
        
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('File quá lớn.');
        }
        
        $fileName = uniqid() . '.' . $fileExtension;
        $uploadPath = UPLOAD_PATH . $directory . '/' . $fileName;
        
        if (!is_dir(dirname($uploadPath))) {
            mkdir(dirname($uploadPath), 0755, true);
        }
        
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            return $directory . '/' . $fileName;
        }
        
        return false;
    }
    
    /**
     * Paginate results
     */
    protected function paginate($query, $page = 1, $perPage = null) {
        $perPage = $perPage ?? ITEMS_PER_PAGE;
        $offset = ($page - 1) * $perPage;
        
        // Add LIMIT to query
        $query .= " LIMIT $offset, $perPage";
        
        return $query;
    }
}
