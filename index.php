<?php
/**
 * Main entry point for the Quản Lý Cà Phê application
 * Coffee Shop Management System
 */

// Set timezone for Vietnam
date_default_timezone_set('Asia/Ho_Chi_Minh');

// Set UTF-8 encoding for Vietnamese language support
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// Start session
session_start();

// Load configuration and bootstrap files
require_once 'config/config.php';
require_once 'app/bootstrap.php';

// Initialize application
try {
    $app = new App();
    $app->run();
} catch (Exception $e) {
    // Log error and show user-friendly message
    error_log($e->getMessage());

    // Show error page in production
    if (ENVIRONMENT === 'production') {
        include 'views/errors/500.php';
    } else {
        // Show detailed error in development
        echo '<h1>Application Error</h1>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
}