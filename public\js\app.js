/**
 * Main JavaScript for Quản Lý Cà Phê
 * Coffee Shop Management System
 */

// Global App Object
window.CafeApp = {
    config: {
        baseUrl: window.location.origin + '/',
        csrfToken: $('meta[name="csrf-token"]').attr('content'),
        locale: 'vi'
    },
    
    // Initialize application
    init: function() {
        this.setupAjax();
        this.setupEventListeners();
        this.setupTooltips();
        this.setupModals();
        this.setupTables();
        this.setupForms();
        this.setupNotifications();
    },
    
    // Setup AJAX defaults
    setupAjax: function() {
        $.ajaxSetup({
            headers: {
                'X-CSRF-Token': this.config.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            beforeSend: function() {
                // Show loading indicator
                $('.loading-overlay').show();
            },
            complete: function() {
                // Hide loading indicator
                $('.loading-overlay').hide();
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                CafeApp.showNotification('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
            }
        });
    },
    
    // Setup global event listeners
    setupEventListeners: function() {
        // Sidebar toggle for mobile
        $(document).on('click', '.sidebar-toggle', function() {
            $('.sidebar').toggleClass('show');
        });
        
        // Close sidebar when clicking outside on mobile
        $(document).on('click', function(e) {
            if ($(window).width() <= 768) {
                if (!$(e.target).closest('.sidebar, .sidebar-toggle').length) {
                    $('.sidebar').removeClass('show');
                }
            }
        });
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Confirm delete actions
        $(document).on('click', '.btn-delete, .delete-btn', function(e) {
            e.preventDefault();
            const message = $(this).data('confirm') || 'Bạn có chắc chắn muốn xóa?';
            if (confirm(message)) {
                if ($(this).is('a')) {
                    window.location.href = $(this).attr('href');
                } else if ($(this).is('button') && $(this).closest('form').length) {
                    $(this).closest('form').submit();
                }
            }
        });
        
        // Number formatting
        $(document).on('input', '.format-number', function() {
            this.value = CafeApp.formatNumber(this.value);
        });
        
        // Currency formatting
        $(document).on('input', '.format-currency', function() {
            this.value = CafeApp.formatCurrency(this.value);
        });
    },
    
    // Setup tooltips
    setupTooltips: function() {
        $('[data-bs-toggle="tooltip"]').tooltip();
    },
    
    // Setup modals
    setupModals: function() {
        // Clear modal content when closed
        $('.modal').on('hidden.bs.modal', function() {
            $(this).find('form')[0]?.reset();
            $(this).find('.is-invalid').removeClass('is-invalid');
            $(this).find('.invalid-feedback').remove();
        });
    },
    
    // Setup DataTables
    setupTables: function() {
        if ($.fn.DataTable) {
            $('.data-table').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: 'no-sort' }
                ]
            });
        }
    },
    
    // Setup forms
    setupForms: function() {
        // Form validation
        $('.needs-validation').on('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            $(this).addClass('was-validated');
        });
        
        // Auto-save forms
        $('.auto-save').on('change', 'input, select, textarea', function() {
            const form = $(this).closest('form');
            CafeApp.autoSave(form);
        });
    },
    
    // Setup notifications
    setupNotifications: function() {
        // Check for new notifications periodically
        if (typeof window.checkNotifications === 'function') {
            setInterval(window.checkNotifications, 30000); // Every 30 seconds
        }
    },
    
    // Show notification
    showNotification: function(message, type = 'info', duration = 5000) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const icon = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';
        
        const alert = $(`
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <i class="${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('body').append(alert);
        
        setTimeout(function() {
            alert.fadeOut(function() {
                $(this).remove();
            });
        }, duration);
    },
    
    // Format number
    formatNumber: function(value) {
        return value.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    // Format currency
    formatCurrency: function(value) {
        const number = value.replace(/\D/g, '');
        return new Intl.NumberFormat('vi-VN').format(number);
    },
    
    // Parse currency to number
    parseCurrency: function(value) {
        return parseFloat(value.replace(/[^\d]/g, '')) || 0;
    },
    
    // Auto-save form
    autoSave: function(form) {
        const formData = new FormData(form[0]);
        const url = form.attr('action') || window.location.href;
        
        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    CafeApp.showNotification('Đã lưu tự động', 'success', 2000);
                }
            },
            error: function() {
                CafeApp.showNotification('Lỗi lưu tự động', 'error', 2000);
            }
        });
    },
    
    // Load content via AJAX
    loadContent: function(url, container, callback) {
        $.get(url)
            .done(function(data) {
                $(container).html(data);
                if (callback) callback(true);
            })
            .fail(function() {
                $(container).html('<div class="alert alert-danger">Không thể tải nội dung</div>');
                if (callback) callback(false);
            });
    },
    
    // Confirm action
    confirm: function(message, callback) {
        if (window.Swal) {
            Swal.fire({
                title: 'Xác nhận',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Đồng ý',
                cancelButtonText: 'Hủy bỏ'
            }).then((result) => {
                if (callback) callback(result.isConfirmed);
            });
        } else {
            const result = confirm(message);
            if (callback) callback(result);
        }
    },
    
    // Show loading
    showLoading: function(element) {
        const $el = $(element);
        $el.prop('disabled', true);
        $el.find('.loading').addClass('show');
        $el.find('.btn-text').text('Đang xử lý...');
    },
    
    // Hide loading
    hideLoading: function(element, originalText = null) {
        const $el = $(element);
        $el.prop('disabled', false);
        $el.find('.loading').removeClass('show');
        if (originalText) {
            $el.find('.btn-text').text(originalText);
        }
    },
    
    // Debounce function
    debounce: function(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    },
    
    // Throttle function
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // Print element
    print: function(element) {
        const printWindow = window.open('', '_blank');
        const content = $(element).html();
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>In ấn</title>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
                <style>
                    body { font-family: 'Roboto', sans-serif; }
                    @media print {
                        .no-print { display: none !important; }
                    }
                </style>
            </head>
            <body>
                ${content}
                <script>
                    window.onload = function() {
                        window.print();
                        window.close();
                    };
                </script>
            </body>
            </html>
        `);
        
        printWindow.document.close();
    }
};

// Initialize when document is ready
$(document).ready(function() {
    CafeApp.init();
});

// Export for global use
window.CafeApp = CafeApp;
