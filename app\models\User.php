<?php
/**
 * User Model
 * Quản L<PERSON> Phê - Coffee Shop Management System
 */

class User extends Model {
    protected $table = 'users';
    protected $fillable = [
        'username', 'password', 'email', 'role_id', 'status'
    ];
    protected $hidden = ['password'];
    
    /**
     * Find user by username or email
     */
    public function findByUsernameOrEmail($identifier) {
        $sql = "SELECT u.*, r.name as role_name, r.permissions 
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                WHERE (u.username = ? OR u.email = ?) AND u.status = 'active'";
        
        return $this->db->fetch($sql, [$identifier, $identifier]);
    }
    
    /**
     * Create new user with hashed password
     */
    public function createUser($data) {
        if (isset($data['password'])) {
            $data['password'] = Auth::hashPassword($data['password']);
        }
        
        return $this->create($data);
    }
    
    /**
     * Update user password
     */
    public function updatePassword($userId, $newPassword) {
        $hashedPassword = Auth::hashPassword($newPassword);
        return $this->update($userId, ['password' => $hashedPassword]);
    }
    
    /**
     * Get user with role information
     */
    public function findWithRole($id) {
        $sql = "SELECT u.*, r.name as role_name, r.permissions, r.description as role_description
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.id = ?";
        
        $result = $this->db->fetch($sql, [$id]);
        return $result ? $this->hideFields($result) : null;
    }
    
    /**
     * Get all users with role information
     */
    public function getAllWithRoles($conditions = [], $orderBy = 'u.created_at DESC') {
        $sql = "SELECT u.*, r.name as role_name, r.description as role_description
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id";
        
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "u.$field = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        
        $results = $this->db->fetchAll($sql, $params);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * Check if username exists
     */
    public function usernameExists($username, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result && $result['count'] > 0;
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result && $result['count'] > 0;
    }
    
    /**
     * Update last login time
     */
    public function updateLastLogin($userId) {
        return $this->update($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * Get users by role
     */
    public function getUsersByRole($roleId) {
        return $this->findAll(['role_id' => $roleId, 'status' => 'active']);
    }
    
    /**
     * Search users
     */
    public function search($keyword, $page = 1, $perPage = null) {
        $perPage = $perPage ?? ITEMS_PER_PAGE;
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT u.*, r.name as role_name 
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.username LIKE ? OR u.email LIKE ? OR r.name LIKE ?
                ORDER BY u.created_at DESC
                LIMIT $offset, $perPage";
        
        $searchTerm = "%$keyword%";
        $results = $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm]);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as count 
                     FROM {$this->table} u 
                     JOIN roles r ON u.role_id = r.id 
                     WHERE u.username LIKE ? OR u.email LIKE ? OR r.name LIKE ?";
        
        $totalResult = $this->db->fetch($countSql, [$searchTerm, $searchTerm, $searchTerm]);
        $total = $totalResult ? $totalResult['count'] : 0;
        
        return [
            'data' => array_map([$this, 'hideFields'], $results),
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }
    
    /**
     * Get active users count
     */
    public function getActiveUsersCount() {
        return $this->count(['status' => 'active']);
    }
    
    /**
     * Get users statistics
     */
    public function getStatistics() {
        $sql = "SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_users,
                    SUM(CASE WHEN status = 'locked' THEN 1 ELSE 0 END) as locked_users,
                    SUM(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_logins
                FROM {$this->table}";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * Get recently registered users
     */
    public function getRecentUsers($limit = 5) {
        $sql = "SELECT u.*, r.name as role_name 
                FROM {$this->table} u 
                JOIN roles r ON u.role_id = r.id 
                ORDER BY u.created_at DESC 
                LIMIT ?";
        
        $results = $this->db->fetchAll($sql, [$limit]);
        return array_map([$this, 'hideFields'], $results);
    }
}
