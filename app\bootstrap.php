<?php
/**
 * Application Bootstrap
 * Qu<PERSON>n <PERSON> - Coffee Shop Management System
 */

// Load database configuration
require_once 'config/database.php';

// Load core classes
require_once 'app/core/App.php';
require_once 'app/core/Controller.php';
require_once 'app/core/Model.php';
require_once 'app/core/View.php';
require_once 'app/core/Router.php';
require_once 'app/core/Session.php';
require_once 'app/core/Auth.php';
require_once 'app/core/Validator.php';
require_once 'app/core/Helper.php';

// Load middleware
require_once 'app/middleware/AuthMiddleware.php';
require_once 'app/middleware/RoleMiddleware.php';
require_once 'app/middleware/CSRFMiddleware.php';

// Autoload controllers
spl_autoload_register(function ($className) {
    $paths = [
        'app/controllers/',
        'app/models/',
        'app/middleware/',
        'app/helpers/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Initialize session
Session::init();

// Set error handler
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return;
    }
    
    $error = [
        'severity' => $severity,
        'message' => $message,
        'file' => $file,
        'line' => $line,
        'time' => date('Y-m-d H:i:s')
    ];
    
    error_log(json_encode($error));
    
    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>Error:</strong> {$message} in <strong>{$file}</strong> on line <strong>{$line}</strong>";
        echo "</div>";
    }
});

// Set exception handler
set_exception_handler(function($exception) {
    $error = [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'time' => date('Y-m-d H:i:s')
    ];
    
    error_log(json_encode($error));
    
    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<h3>Uncaught Exception:</h3>";
        echo "<p><strong>Message:</strong> " . $exception->getMessage() . "</p>";
        echo "<p><strong>File:</strong> " . $exception->getFile() . "</p>";
        echo "<p><strong>Line:</strong> " . $exception->getLine() . "</p>";
        echo "<pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        // Redirect to error page in production
        header('Location: ' . BASE_URL . 'error/500');
        exit;
    }
});

// Helper functions
function redirect($url) {
    if (strpos($url, 'http') !== 0) {
        $url = BASE_URL . ltrim($url, '/');
    }
    header("Location: $url");
    exit;
}

function back() {
    $referer = $_SERVER['HTTP_REFERER'] ?? BASE_URL;
    header("Location: $referer");
    exit;
}

function asset($path) {
    return BASE_URL . 'public/' . ltrim($path, '/');
}

function url($path = '') {
    return BASE_URL . ltrim($path, '/');
}

function old($key, $default = '') {
    return Session::getFlash('old_' . $key, $default);
}

function errors($key = null) {
    if ($key) {
        return Session::getFlash('errors_' . $key, []);
    }
    return Session::getFlash('errors', []);
}

function success($message = null) {
    if ($message) {
        Session::setFlash('success', $message);
        return;
    }
    return Session::getFlash('success');
}

function error($message = null) {
    if ($message) {
        Session::setFlash('error', $message);
        return;
    }
    return Session::getFlash('error');
}

function csrf_token() {
    return Session::get('csrf_token', '');
}

function csrf_field() {
    return '<input type="hidden" name="' . CSRF_TOKEN_NAME . '" value="' . csrf_token() . '">';
}

function formatCurrency($amount) {
    return number_format($amount, 0, ',', '.') . ' ' . CURRENCY_SYMBOL;
}

function formatDate($date, $format = DATE_FORMAT) {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

function formatDateTime($datetime, $format = DATETIME_FORMAT) {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}
