<?php
/**
 * Session Management Class
 * Quản <PERSON>ê - Coffee Shop Management System
 */

class Session {
    
    /**
     * Initialize session
     */
    public static function init() {
        if (session_status() === PHP_SESSION_NONE) {
            // Configure session settings
            ini_set('session.cookie_lifetime', SESSION_LIFETIME);
            ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            
            session_start();
            
            // Generate CSRF token if not exists
            if (!self::has('csrf_token')) {
                self::set('csrf_token', self::generateCsrfToken());
            }
            
            // Regenerate session ID periodically
            if (!self::has('last_regeneration')) {
                self::set('last_regeneration', time());
            } elseif (time() - self::get('last_regeneration') > 300) { // 5 minutes
                session_regenerate_id(true);
                self::set('last_regeneration', time());
            }
        }
    }
    
    /**
     * Set session value
     */
    public static function set($key, $value) {
        $_SESSION[$key] = $value;
    }
    
    /**
     * Get session value
     */
    public static function get($key, $default = null) {
        return $_SESSION[$key] ?? $default;
    }
    
    /**
     * Check if session key exists
     */
    public static function has($key) {
        return isset($_SESSION[$key]);
    }
    
    /**
     * Remove session key
     */
    public static function remove($key) {
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }
    
    /**
     * Clear all session data
     */
    public static function clear() {
        session_unset();
    }
    
    /**
     * Destroy session
     */
    public static function destroy() {
        session_destroy();
    }
    
    /**
     * Set flash message
     */
    public static function setFlash($key, $value) {
        $_SESSION['flash'][$key] = $value;
    }
    
    /**
     * Get flash message (and remove it)
     */
    public static function getFlash($key, $default = null) {
        $value = $_SESSION['flash'][$key] ?? $default;
        if (isset($_SESSION['flash'][$key])) {
            unset($_SESSION['flash'][$key]);
        }
        return $value;
    }
    
    /**
     * Check if flash message exists
     */
    public static function hasFlash($key) {
        return isset($_SESSION['flash'][$key]);
    }
    
    /**
     * Set old input data
     */
    public static function setOldInput($data) {
        foreach ($data as $key => $value) {
            self::setFlash('old_' . $key, $value);
        }
    }
    
    /**
     * Set validation errors
     */
    public static function setErrors($errors) {
        if (is_array($errors)) {
            foreach ($errors as $field => $messages) {
                self::setFlash('errors_' . $field, $messages);
            }
        } else {
            self::setFlash('errors', $errors);
        }
    }
    
    /**
     * Generate CSRF token
     */
    public static function generateCsrfToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyCsrfToken($token) {
        $sessionToken = self::get('csrf_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }
    
    /**
     * Regenerate CSRF token
     */
    public static function regenerateCsrfToken() {
        self::set('csrf_token', self::generateCsrfToken());
    }
    
    /**
     * Get session ID
     */
    public static function getId() {
        return session_id();
    }
    
    /**
     * Regenerate session ID
     */
    public static function regenerateId($deleteOld = true) {
        session_regenerate_id($deleteOld);
        self::set('last_regeneration', time());
    }
    
    /**
     * Check if session is active
     */
    public static function isActive() {
        return session_status() === PHP_SESSION_ACTIVE;
    }
    
    /**
     * Get all session data
     */
    public static function all() {
        return $_SESSION;
    }
    
    /**
     * Set user session data
     */
    public static function setUser($user) {
        self::set('user_id', $user['id']);
        self::set('user_data', $user);
        self::set('logged_in', true);
        self::set('login_time', time());
    }
    
    /**
     * Get user session data
     */
    public static function getUser() {
        return self::get('user_data');
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        return self::get('logged_in', false) && self::has('user_id');
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        self::remove('user_id');
        self::remove('user_data');
        self::remove('logged_in');
        self::remove('login_time');
        self::regenerateCsrfToken();
    }
    
    /**
     * Check session timeout
     */
    public static function checkTimeout() {
        $loginTime = self::get('login_time');
        if ($loginTime && (time() - $loginTime) > SESSION_LIFETIME) {
            self::logout();
            return true;
        }
        return false;
    }
    
    /**
     * Update last activity time
     */
    public static function updateActivity() {
        self::set('last_activity', time());
    }
    
    /**
     * Get last activity time
     */
    public static function getLastActivity() {
        return self::get('last_activity', 0);
    }
}
