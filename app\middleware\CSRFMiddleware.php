<?php
/**
 * CSRF Protection Middleware
 * Quản Lý Cà Phê - Coffee Shop Management System
 */

class CSRFMiddleware {
    
    /**
     * Handle CSRF token verification
     */
    public function handle() {
        // Only check CSRF for state-changing requests
        if ($this->shouldVerifyToken()) {
            $this->verifyToken();
        }
    }
    
    /**
     * Check if we should verify CSRF token
     */
    private function shouldVerifyToken() {
        $method = $_SERVER['REQUEST_METHOD'];
        
        // Only verify for POST, PUT, PATCH, DELETE requests
        if (!in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            return false;
        }
        
        // Skip verification for API endpoints that use other authentication
        $uri = $_SERVER['REQUEST_URI'];
        if (strpos($uri, '/api/') !== false) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Verify CSRF token
     */
    private function verifyToken() {
        $token = $this->getTokenFromRequest();
        
        if (!$token || !Session::verifyCsrfToken($token)) {
            $this->handleInvalidToken();
        }
    }
    
    /**
     * Get CSRF token from request
     */
    private function getTokenFromRequest() {
        // Check POST data first
        if (isset($_POST[CSRF_TOKEN_NAME])) {
            return $_POST[CSRF_TOKEN_NAME];
        }
        
        // Check headers (for AJAX requests)
        $headers = getallheaders();
        if (isset($headers['X-CSRF-Token'])) {
            return $headers['X-CSRF-Token'];
        }
        
        if (isset($headers['X-Requested-With']) && $headers['X-Requested-With'] === 'XMLHttpRequest') {
            // For AJAX requests, also check common header names
            if (isset($headers['X-CSRFToken'])) {
                return $headers['X-CSRFToken'];
            }
            
            if (isset($headers['X-XSRF-TOKEN'])) {
                return $headers['X-XSRF-TOKEN'];
            }
        }
        
        return null;
    }
    
    /**
     * Handle invalid CSRF token
     */
    private function handleInvalidToken() {
        // Log the attempt
        $this->logCSRFAttempt();
        
        // Set error message
        $message = 'Token bảo mật không hợp lệ. Vui lòng thử lại.';
        
        if ($this->isAjaxRequest()) {
            $this->jsonResponse([
                'success' => false,
                'message' => $message,
                'error' => 'csrf_token_mismatch',
                'reload' => true
            ], 419);
        } else {
            Session::setFlash('error', $message);
            $this->redirectBack();
        }
    }
    
    /**
     * Check if request is AJAX
     */
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Redirect back to previous page
     */
    private function redirectBack() {
        $referer = $_SERVER['HTTP_REFERER'] ?? BASE_URL;
        header("Location: $referer");
        exit;
    }
    
    /**
     * Send JSON response
     */
    private function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Log CSRF attempt
     */
    private function logCSRFAttempt() {
        $logData = [
            'type' => 'csrf_attempt',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'referer' => $_SERVER['HTTP_REFERER'] ?? 'unknown',
            'user_id' => Auth::id(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        error_log('CSRF Attempt: ' . json_encode($logData));
    }
    
    /**
     * Generate new CSRF token
     */
    public static function generateToken() {
        Session::regenerateCsrfToken();
        return Session::get('csrf_token');
    }
    
    /**
     * Get current CSRF token
     */
    public static function getToken() {
        return Session::get('csrf_token', '');
    }
    
    /**
     * Generate CSRF token field for forms
     */
    public static function field() {
        return '<input type="hidden" name="' . CSRF_TOKEN_NAME . '" value="' . self::getToken() . '">';
    }
    
    /**
     * Generate CSRF meta tag for AJAX requests
     */
    public static function metaTag() {
        return '<meta name="csrf-token" content="' . self::getToken() . '">';
    }
}
