<?php
/**
 * Authentication Controller
 * Qu<PERSON>n <PERSON> - Coffee Shop Management System
 */

class AuthController extends Controller {
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Show login form
     */
    public function login() {
        // Redirect if already authenticated
        if (Auth::check()) {
            $this->redirect('dashboard');
        }
        
        $this->view->setLayout('layouts/auth');
        $this->view('auth/login', [
            'title' => 'Đăng nhập - ' . APP_NAME
        ]);
    }
    
    /**
     * Handle login authentication
     */
    public function authenticate() {
        if (!$this->isPost()) {
            $this->redirect('login');
        }
        
        // Verify CSRF token
        $this->verifyCsrf();
        
        // Get input data
        $username = $this->input('username');
        $password = $this->input('password');
        $remember = $this->input('remember');
        
        // Validate input
        $validator = new Validator();
        $isValid = $validator->validate([
            'username' => $username,
            'password' => $password
        ], [
            'username' => 'required',
            'password' => 'required'
        ]);
        
        if (!$isValid) {
            Session::setErrors($validator->getErrors());
            Session::setOldInput($_POST);
            $this->redirect('login');
        }
        
        // Attempt authentication
        if (Auth::attempt($username, $password)) {
            // Log successful login
            Helper::logActivity('user_login', 'User logged in successfully');
            
            // Set success message
            $this->setSuccess('Đăng nhập thành công!');
            
            // Handle remember me functionality
            if ($remember) {
                $this->setRememberToken();
            }
            
            // Redirect to intended URL or dashboard
            $intendedUrl = Session::get('intended_url', 'dashboard');
            Session::remove('intended_url');
            $this->redirect($intendedUrl);
            
        } else {
            // Log failed login attempt
            Helper::logActivity('login_failed', "Failed login attempt for: $username");
            
            // Set error message
            $this->setError('Tên đăng nhập hoặc mật khẩu không chính xác.');
            
            // Keep old input (except password)
            Session::setOldInput(['username' => $username]);
            
            $this->redirect('login');
        }
    }
    
    /**
     * Handle logout
     */
    public function logout() {
        if (Auth::check()) {
            // Log logout activity
            Helper::logActivity('user_logout', 'User logged out');
            
            // Clear remember token if exists
            $this->clearRememberToken();
            
            // Logout user
            Auth::logout();
            
            $this->setSuccess('Đã đăng xuất thành công.');
        }
        
        $this->redirect('login');
    }
    
    /**
     * Show forgot password form
     */
    public function forgotPassword() {
        if (Auth::check()) {
            $this->redirect('dashboard');
        }
        
        $this->view->setLayout('layouts/auth');
        $this->view('auth/forgot-password', [
            'title' => 'Quên mật khẩu - ' . APP_NAME
        ]);
    }
    
    /**
     * Handle forgot password request
     */
    public function sendResetLink() {
        if (!$this->isPost()) {
            $this->redirect('forgot-password');
        }
        
        $this->verifyCsrf();
        
        $email = $this->input('email');
        
        // Validate email
        $validator = new Validator();
        $isValid = $validator->validate(['email' => $email], [
            'email' => 'required|email'
        ]);
        
        if (!$isValid) {
            Session::setErrors($validator->getErrors());
            Session::setOldInput($_POST);
            $this->redirect('forgot-password');
        }
        
        // Check if user exists
        $userModel = $this->model('User');
        $user = $userModel->findFirst(['email' => $email, 'status' => 'active']);
        
        if ($user) {
            // Generate reset token
            $token = $this->generateResetToken($user['id']);
            
            // Send reset email (implement email sending)
            $this->sendResetEmail($user, $token);
            
            Helper::logActivity('password_reset_requested', "Password reset requested for: {$user['email']}");
        }
        
        // Always show success message for security
        $this->setSuccess('Nếu email tồn tại, liên kết đặt lại mật khẩu đã được gửi.');
        $this->redirect('login');
    }
    
    /**
     * Show reset password form
     */
    public function resetPassword($token = null) {
        if (Auth::check()) {
            $this->redirect('dashboard');
        }
        
        if (!$token || !$this->validateResetToken($token)) {
            $this->setError('Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn.');
            $this->redirect('login');
        }
        
        $this->view->setLayout('layouts/auth');
        $this->view('auth/reset-password', [
            'title' => 'Đặt lại mật khẩu - ' . APP_NAME,
            'token' => $token
        ]);
    }
    
    /**
     * Handle password reset
     */
    public function updatePassword() {
        if (!$this->isPost()) {
            $this->redirect('login');
        }
        
        $this->verifyCsrf();
        
        $token = $this->input('token');
        $password = $this->input('password');
        $passwordConfirmation = $this->input('password_confirmation');
        
        // Validate input
        $validator = new Validator();
        $isValid = $validator->validate([
            'token' => $token,
            'password' => $password,
            'password_confirmation' => $passwordConfirmation
        ], [
            'token' => 'required',
            'password' => 'required|min:8|confirmed',
            'password_confirmation' => 'required'
        ]);
        
        if (!$isValid) {
            Session::setErrors($validator->getErrors());
            $this->redirect("reset-password/$token");
        }
        
        // Validate token and get user
        $userId = $this->validateResetToken($token);
        if (!$userId) {
            $this->setError('Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn.');
            $this->redirect('login');
        }
        
        // Update password
        $userModel = $this->model('User');
        if ($userModel->updatePassword($userId, $password)) {
            // Clear reset token
            $this->clearResetToken($token);
            
            Helper::logActivity('password_reset_completed', "Password reset completed for user ID: $userId");
            
            $this->setSuccess('Mật khẩu đã được đặt lại thành công. Vui lòng đăng nhập.');
        } else {
            $this->setError('Có lỗi xảy ra khi đặt lại mật khẩu. Vui lòng thử lại.');
        }
        
        $this->redirect('login');
    }
    
    /**
     * Set remember token
     */
    private function setRememberToken() {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + (30 * 24 * 60 * 60); // 30 days
        
        setcookie('remember_token', $token, $expiry, '/', '', false, true);
        
        // Store token in database (implement if needed)
        // $this->storeRememberToken(Auth::id(), $token, $expiry);
    }
    
    /**
     * Clear remember token
     */
    private function clearRememberToken() {
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
            // Remove from database (implement if needed)
            // $this->removeRememberToken($_COOKIE['remember_token']);
        }
    }
    
    /**
     * Generate password reset token
     */
    private function generateResetToken($userId) {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + (60 * 60); // 1 hour
        
        // Store token in session or database
        Session::set("reset_token_$token", [
            'user_id' => $userId,
            'expires' => $expiry
        ]);
        
        return $token;
    }
    
    /**
     * Validate reset token
     */
    private function validateResetToken($token) {
        $tokenData = Session::get("reset_token_$token");
        
        if (!$tokenData || $tokenData['expires'] < time()) {
            return false;
        }
        
        return $tokenData['user_id'];
    }
    
    /**
     * Clear reset token
     */
    private function clearResetToken($token) {
        Session::remove("reset_token_$token");
    }
    
    /**
     * Send reset email (placeholder - implement with actual email service)
     */
    private function sendResetEmail($user, $token) {
        $resetUrl = url("reset-password/$token");
        
        // Implement email sending logic here
        // For now, just log the reset URL
        error_log("Password reset URL for {$user['email']}: $resetUrl");
        
        return true;
    }
}
